<?php
session_start();
include 'db_connect.php';

$error = "";

// إنشاء رمز CSRF إذا لم يكن موجودًا
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// تهيئة عداد محاولات تسجيل الدخول
if (!isset($_SESSION['login_attempts'])) {
    $_SESSION['login_attempts'] = 0;
    $_SESSION['last_attempt_time'] = time();
}

// التحقق من تجاوز الحد الأقصى لمحاولات تسجيل الدخول
$max_attempts = 5; // الحد الأقصى للمحاولات
$lockout_time = 5 * 60; // وقت الحظر بالثواني (15 دقيقة)

if ($_SESSION['login_attempts'] >= $max_attempts) {
    $time_passed = time() - $_SESSION['last_attempt_time'];
    if ($time_passed < $lockout_time) {
        $remaining_time = $lockout_time - $time_passed;
        $minutes = ceil($remaining_time / 60);
        $error = "تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة بعد $minutes دقيقة.";
    } else {
        // إعادة تعيين عداد المحاولات بعد انتهاء فترة الحظر
        $_SESSION['login_attempts'] = 0;
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // التحقق من رمز CSRF
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = "خطأ في التحقق من الأمان. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.";
    } else {
        $username = $_POST['username'];
        $password = $_POST['password'];
        
        // إذا لم يتجاوز الحد الأقصى للمحاولات
        if ($_SESSION['login_attempts'] < $max_attempts) {
            // استخدام الاستعلامات المُجهزة لمنع هجمات SQL Injection
            $stmt = $conn->prepare("SELECT * FROM residence_managers WHERE username = ?");
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $user = $result->fetch_assoc();
                
                // التحقق من كلمة المرور باستخدام password_verify
                if (password_verify($password, $user['password'])) {
                    // إعادة تعيين عداد محاولات تسجيل الدخول
                    $_SESSION['login_attempts'] = 0;
                    $_SESSION['admin'] = $user;
                    
                    // توجيه المستخدم حسب دوره
                    if ($_SESSION['admin']['role'] == 'manager') {
                        header("Location: manage_requests.php");
                    } else if ($_SESSION['admin']['role'] == 'residence_manager') {
                        header("Location: confirm_release.php");
                    }
                    exit();
                } else {
                    // زيادة عداد محاولات تسجيل الدخول
                    $_SESSION['login_attempts']++;
                    $_SESSION['last_attempt_time'] = time();
                    $error = "اسم المستخدم أو كلمة المرور غير صحيحة";
                }
            } else {
                // زيادة عداد محاولات تسجيل الدخول
                $_SESSION['login_attempts']++;
                $_SESSION['last_attempt_time'] = time();
                $error = "اسم المستخدم أو كلمة المرور غير صحيحة";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول المسؤولين</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
        }
        .login-container {
            max-width: 450px;
            margin: 0 auto;
            padding: 15px;
        }
        .login-card {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            background-color: white;
            padding: 30px;
            margin-top: 20px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header img {
            max-width: 80px;
            margin-bottom: 15px;
        }
        .form-floating {
            margin-bottom: 20px;
        }
        .btn-login {
            width: 100%;
            padding: 12px;
            font-weight: bold;
        }
        .home-link {
            display: block;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-university fa-4x text-primary mb-3"></i>
                <h2>منصة التحويلات بين الإقامات الجامعية</h2>
                <p class="text-muted">تسجيل دخول المسؤولين</p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <form action="admin_login.php" method="POST">
                <!-- إضافة حقل مخفي لرمز CSRF -->
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                
                <div class="form-floating mb-3">
                    <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                    <label for="username">اسم المستخدم</label>
                </div>
                
                <div class="form-floating mb-4">
                    <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                    <label for="password">كلمة المرور</label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="text-center mt-4">
                <a href="index.html" class="btn btn-outline-secondary">
                    <i class="fas fa-home me-2"></i>
                    العودة إلى الصفحة الرئيسية
                </a>
            </div>
        </div>
        
        <div class="text-center mt-4 text-muted">
            <p>&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
        </div>
    </div>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>