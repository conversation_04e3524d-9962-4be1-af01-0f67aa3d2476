<?php
session_start();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأسئلة الشائعة | منصة التحويلات بين الإقامات الجامعية</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .main-content {
            background: white;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            position: relative;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,100 1000,0 1000,100"/></svg>');
            background-size: cover;
        }

        .content-section {
            padding: 60px 0;
        }

        .faq-item {
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .faq-question {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: right;
            font-weight: 600;
            color: var(--dark-color);
            transition: all 0.3s ease;
            position: relative;
        }

        .faq-question:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .faq-question::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
        }

        .faq-question.active::after {
            transform: translateY(-50%) rotate(180deg);
        }

        .faq-answer {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-answer.active {
            padding: 20px;
            max-height: 500px;
        }

        .category-section {
            margin-bottom: 50px;
        }

        .category-title {
            color: var(--primary-color);
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 30px;
        }

        .search-box {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 40px;
        }

        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .contact-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .contact-icon {
            font-size: 1.5rem;
            margin-left: 15px;
            width: 40px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h4 mb-0 text-white">
                    <i class="fas fa-question-circle me-2"></i>
                    الأسئلة الشائعة
                </h1>
                <a href="index.html" class="btn btn-outline-light">
                    <i class="fas fa-home me-1"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">الأسئلة الشائعة</h1>
            <p class="lead mb-0">إجابات على أكثر الأسئلة شيوعاً حول منصة التحويلات بين الإقامات الجامعية</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-section">
            <div class="container">
                <!-- Search Box -->
                <div class="search-box">
                    <div class="row">
                        <div class="col-md-8 mx-auto">
                            <h4 class="text-center mb-4">
                                <i class="fas fa-search me-2"></i>
                                ابحث في الأسئلة الشائعة
                            </h4>
                            <input type="text" class="form-control search-input" id="searchInput" 
                                   placeholder="اكتب كلمة البحث هنا..." onkeyup="searchFAQ()">
                        </div>
                    </div>
                </div>

                <!-- FAQ Categories -->
                
                <!-- للطلبة -->
                <div class="category-section">
                    <h2 class="category-title">
                        <i class="fas fa-user-graduate me-2"></i>
                        أسئلة الطلبة
                    </h2>
                    
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            كيف يمكنني تقديم طلب تحويل بين الإقامات؟
                        </button>
                        <div class="faq-answer">
                            <p>لتقديم طلب تحويل، اتبع الخطوات التالية:</p>
                            <ol>
                                <li>سجل دخولك باستخدام رقم البكالوريا</li>
                                <li>اذهب إلى صفحة "تقديم طلب جديد"</li>
                                <li>املأ النموذج بالمعلومات المطلوبة</li>
                                <li>اختر الإقامة المطلوبة</li>
                                <li>أرسل الطلب</li>
                            </ol>
                        </div>
                    </div>

                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            ما هي المستندات المطلوبة لتقديم طلب التحويل؟
                        </button>
                        <div class="faq-answer">
                            <p>المستندات المطلوبة تشمل:</p>
                            <ul>
                                <li>شهادة البكالوريا</li>
                                <li>بطاقة الهوية الوطنية</li>
                                <li>شهادة التسجيل الجامعي</li>
                                <li>إثبات الإقامة الحالية</li>
                                <li>أي مستندات إضافية حسب الحالة</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            كم تستغرق مدة معالجة طلب التحويل؟
                        </button>
                        <div class="faq-answer">
                            <p>عادةً ما تستغرق معالجة طلب التحويل من 5 إلى 10 أيام عمل، حسب:</p>
                            <ul>
                                <li>اكتمال المستندات المطلوبة</li>
                                <li>توفر الأماكن في الإقامة المطلوبة</li>
                                <li>حجم الطلبات المقدمة</li>
                                <li>فترة السنة الدراسية</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            كيف يمكنني متابعة حالة طلبي؟
                        </button>
                        <div class="faq-answer">
                            <p>يمكنك متابعة حالة طلبك من خلال:</p>
                            <ul>
                                <li>تسجيل الدخول إلى حسابك</li>
                                <li>الذهاب إلى صفحة "طلباتي"</li>
                                <li>عرض تفاصيل الطلب وحالته</li>
                                <li>ستظهر لك التحديثات تلقائياً</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- للمسؤولين -->
                <div class="category-section">
                    <h2 class="category-title">
                        <i class="fas fa-user-shield me-2"></i>
                        أسئلة المسؤولين
                    </h2>
                    
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            كيف يمكنني إدارة الطلبات المقدمة لإقامتي؟
                        </button>
                        <div class="faq-answer">
                            <p>لإدارة الطلبات:</p>
                            <ol>
                                <li>سجل دخولك كمسؤول إقامة</li>
                                <li>اذهب إلى صفحة "إدارة الطلبات"</li>
                                <li>راجع الطلبات المقدمة</li>
                                <li>اتخذ القرار المناسب (قبول/رفض)</li>
                                <li>أضف ملاحظات إذا لزم الأمر</li>
                            </ol>
                        </div>
                    </div>

                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            ما هي معايير قبول طلبات التحويل؟
                        </button>
                        <div class="faq-answer">
                            <p>معايير القبول تشمل:</p>
                            <ul>
                                <li>اكتمال المستندات المطلوبة</li>
                                <li>توفر أماكن شاغرة في الإقامة</li>
                                <li>أولوية الطلبات حسب تاريخ التقديم</li>
                                <li>الظروف الخاصة (المسافة، الحالة الصحية، إلخ)</li>
                                <li>التزام الطالب بالقوانين والأنظمة</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            كيف يمكنني إنشاء تقارير وإحصائيات؟
                        </button>
                        <div class="faq-answer">
                            <p>لإنشاء التقارير والإحصائيات:</p>
                            <ul>
                                <li>اذهب إلى صفحة "التقارير والإحصائيات"</li>
                                <li>اختر نوع التقرير المطلوب</li>
                                <li>حدد الفترة الزمنية</li>
                                <li>اختر معايير التصفية</li>
                                <li>احفظ أو اطبع التقرير</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- تقنية -->
                <div class="category-section">
                    <h2 class="category-title">
                        <i class="fas fa-cog me-2"></i>
                        أسئلة تقنية
                    </h2>
                    
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            ماذا أفعل إذا نسيت كلمة المرور؟
                        </button>
                        <div class="faq-answer">
                            <p>إذا نسيت كلمة المرور:</p>
                            <ol>
                                <li>اذهب إلى صفحة تسجيل الدخول</li>
                                <li>اضغط على "نسيت كلمة المرور"</li>
                                <li>أدخل اسم المستخدم أو رقم البكالوريا</li>
                                <li>اتبع التعليمات لإعادة تعيين كلمة المرور</li>
                                <li>ستصلك رسالة على البريد الإلكتروني</li>
                            </ol>
                        </div>
                    </div>

                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            هل النظام آمن لحماية بياناتي؟
                        </button>
                        <div class="faq-answer">
                            <p>نعم، النظام آمن تماماً ويستخدم:</p>
                            <ul>
                                <li>تشفير SSL لحماية البيانات</li>
                                <li>خوارزميات تشفير قوية لكلمات المرور</li>
                                <li>حماية من هجمات SQL Injection</li>
                                <li>حماية من هجمات XSS</li>
                                <li>نسخ احتياطية منتظمة للبيانات</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            ما هي متطلبات النظام للاستخدام؟
                        </button>
                        <div class="faq-answer">
                            <p>متطلبات النظام:</p>
                            <ul>
                                <li>متصفح ويب حديث (Chrome, Firefox, Safari, Edge)</li>
                                <li>اتصال بالإنترنت</li>
                                <li>تفعيل JavaScript</li>
                                <li>دقة شاشة 1024x768 أو أعلى</li>
                                <li>لا يحتاج إلى تثبيت برامج إضافية</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Contact Info -->
                <div class="contact-info">
                    <h3 class="text-center mb-4">
                        <i class="fas fa-headset me-2"></i>
                        لم تجد إجابة لسؤالك؟
                    </h3>
                    <p class="text-center mb-4">يمكنك التواصل معنا للحصول على المساعدة</p>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="contact-item">
                                <i class="fas fa-phone contact-icon"></i>
                                <div>
                                    <strong>الهاتف</strong><br>
                                    ************
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="contact-item">
                                <i class="fas fa-envelope contact-icon"></i>
                                <div>
                                    <strong>البريد الإلكتروني</strong><br>
                                    <EMAIL>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="contact-item">
                                <i class="fas fa-clock contact-icon"></i>
                                <div>
                                    <strong>ساعات العمل</strong><br>
                                    الأحد - الخميس: 8:00 - 16:00
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="contact.php" class="btn btn-light btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            اتصل بنا الآن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 منصة التحويلات بين الإقامات الجامعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleFAQ(element) {
            const answer = element.nextElementSibling;
            const isActive = answer.classList.contains('active');
            
            // إغلاق جميع الأسئلة الأخرى
            document.querySelectorAll('.faq-answer').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.faq-question').forEach(item => {
                item.classList.remove('active');
            });
            
            // فتح/إغلاق السؤال المحدد
            if (!isActive) {
                answer.classList.add('active');
                element.classList.add('active');
            }
        }

        function searchFAQ() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const faqItems = document.querySelectorAll('.faq-item');
            
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question').textContent.toLowerCase();
                const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
                
                if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.style.display = 'block';
                    if (searchTerm !== '') {
                        item.querySelector('.faq-answer').classList.add('active');
                        item.querySelector('.faq-question').classList.add('active');
                    }
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // فتح أول سؤال عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const firstQuestion = document.querySelector('.faq-question');
            if (firstQuestion) {
                toggleFAQ(firstQuestion);
            }
        });
    </script>
</body>
</html> 