<?php
// استيراد ملف الإعدادات
require_once __DIR__ . '/config.php';

// الاتصال بقاعدة البيانات
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

// التحقق من الاتصال
if ($conn->connect_error) {
    // تسجيل الخطأ
    log_message("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error, "error");
    die("فشل الاتصال: " . $conn->connect_error);
}

// تعيين ترميز UTF-8 لدعم اللغة العربية بشكل صحيح
$conn->set_charset("utf8mb4");

// تعيين وضع الخطأ للاستعلامات لتسهيل تصحيح الأخطاء
$conn->query("SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'");

// دالة للحماية من SQL Injection
function clean_input($data) {
    global $conn;
    return $conn->real_escape_string(trim($data));
}
?>

