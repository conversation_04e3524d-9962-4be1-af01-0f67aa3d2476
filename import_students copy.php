<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

// تضمين ملف الاتصال بقاعدة البيانات
include 'db_connect.php';

// تحميل مكتبة PhpSpreadsheet
require 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

$message = "";
$message_type = "";
$imported_count = 0;
$errors = [];

// التحقق من وجود ملف تم رفعه
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_FILES["excelFile"]) && $_FILES["excelFile"]["error"] == 0) {
    
    // التحقق من نوع الملف
    $allowed_extensions = ['xls', 'xlsx'];
    $file_extension = pathinfo($_FILES["excelFile"]["name"], PATHINFO_EXTENSION);
    
    if (in_array(strtolower($file_extension), $allowed_extensions)) {
        try {
            // قراءة ملف Excel
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($_FILES["excelFile"]["tmp_name"]);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            // تخطي الصف الأول (عناوين الأعمدة)
            $header = array_shift($rows);
            
            // استرجاع قائمة الإقامات الجامعية
            $residences_query = "SELECT * FROM residences";
            $residences_result = $conn->query($residences_query);
            $residences = [];
            
            if ($residences_result && $residences_result->num_rows > 0) {
                while ($residence = $residences_result->fetch_assoc()) {
                    $residences[$residence['name']] = $residence['gender'];
                }
            }
            
            // معالجة كل صف من البيانات
            foreach ($rows as $index => $row) {
                // تخطي الصفوف الفارغة
                if (empty($row[0])) continue;
                
                // استخراج البيانات من الصف بترتيب حقول جدول الطلبة
                // ترتيب الأعمدة: رقم البكالوريا، اللقب، الاسم، تاريخ الميلاد، مكان الميلاد، الجنس، الإقامة، التخصص، السنة الدراسية
                $baccalaureate_number = trim($row[0]);
                $last_name = trim($row[1]);
                $first_name = trim($row[2]);
                $birth_date = trim($row[3]);
                $birth_place = trim($row[4]);
                $gender = trim($row[5]);
                $residence = trim($row[6]);
                $specialization = trim($row[7]);
                $study_year = trim($row[8]);
                
                // التحقق من صحة البيانات الأساسية
                if (empty($baccalaureate_number) || empty($last_name) || empty($first_name)) {
                    $errors[] = "الصف " . ($index + 2) . ": البيانات الأساسية غير مكتملة (رقم البكالوريا، اللقب، الاسم).";
                    continue;
                }
                
                // تنسيق تاريخ الميلاد
                if (!empty($birth_date)) {
                    // محاولة تحويل التاريخ إلى التنسيق المطلوب
                    if (is_numeric($birth_date)) {
                        // إذا كان التاريخ بتنسيق Excel الرقمي
                        $unix_date = ($birth_date - 25569) * 86400;
                        $birth_date = date('Y-m-d', $unix_date);
                    } else {
                        // محاولة تحليل التاريخ بتنسيقات مختلفة
                        $date_obj = date_create_from_format('d/m/Y', $birth_date);
                        if (!$date_obj) {
                            $date_obj = date_create_from_format('Y-m-d', $birth_date);
                        }
                        if ($date_obj) {
                            $birth_date = date_format($date_obj, 'Y-m-d');
                        } else {
                            $errors[] = "الصف " . ($index + 2) . ": تنسيق تاريخ الميلاد غير صحيح.";
                            continue;
                        }
                    }
                } else {
                    $birth_date = null;
                }
                
                // إزالة التحقق من تطابق جنس الطالب مع جنس الإقامة
                // سيتم استيراد جميع الطلبة بغض النظر عن الجنس
                
                // التحقق من عدم وجود طالب بنفس رقم البكالوريا
                $check_stmt = $conn->prepare("SELECT id FROM students WHERE baccalaureate_number = ?");
                $check_stmt->bind_param("s", $baccalaureate_number);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows > 0) {
                    // تحديث بيانات الطالب الموجود
                    $student = $check_result->fetch_assoc();
                    $student_id = $student['id'];
                    
                    $update_stmt = $conn->prepare("UPDATE students SET 
                        last_name = ?, 
                        first_name = ?, 
                        birth_date = ?, 
                        birth_place = ?, 
                        gender = ?, 
                        residence = ?, 
                        specialization = ?, 
                        study_year = ? 
                        WHERE id = ?");
                    
                    $update_stmt->bind_param("ssssssssi", 
                        $last_name, 
                        $first_name, 
                        $birth_date, 
                        $birth_place, 
                        $gender, 
                        $residence, 
                        $specialization, 
                        $study_year, 
                        $student_id
                    );
                    
                    if ($update_stmt->execute()) {
                        $imported_count++;
                    } else {
                        $errors[] = "الصف " . ($index + 2) . ": خطأ في تحديث بيانات الطالب: " . $conn->error;
                    }
                } else {
                    // إضافة طالب جديد
                    $insert_stmt = $conn->prepare("INSERT INTO students 
                        (baccalaureate_number, last_name, first_name, birth_date, birth_place, gender, residence, specialization, study_year) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    
                    $insert_stmt->bind_param("sssssssss", 
                        $baccalaureate_number, 
                        $last_name, 
                        $first_name, 
                        $birth_date, 
                        $birth_place, 
                        $gender, 
                        $residence, 
                        $specialization, 
                        $study_year
                    );
                    
                    if ($insert_stmt->execute()) {
                        $imported_count++;
                    } else {
                        $errors[] = "الصف " . ($index + 2) . ": خطأ في إضافة الطالب: " . $conn->error;
                    }
                }
            }
            
            // إعداد رسالة النجاح
            if ($imported_count > 0) {
                $message = "تم استيراد $imported_count طالب بنجاح.";
                $message_type = "success";
            } else {
                $message = "لم يتم استيراد أي طالب.";
                $message_type = "warning";
            }
            
        } catch (Exception $e) {
            $message = "حدث خطأ أثناء معالجة الملف: " . $e->getMessage();
            $message_type = "danger";
        }
    } else {
        $message = "نوع الملف غير مدعوم. يرجى استخدام ملفات Excel (.xls أو .xlsx).";
        $message_type = "danger";
    }
} else {
    $message = "لم يتم تحديد ملف أو حدث خطأ أثناء رفع الملف.";
    $message_type = "danger";
}

// تخزين الرسالة في الجلسة لعرضها بعد إعادة التوجيه
$_SESSION['import_message'] = $message;
$_SESSION['import_message_type'] = $message_type;
$_SESSION['import_errors'] = $errors;
$_SESSION['imported_count'] = $imported_count;

// إعادة التوجيه إلى صفحة إدارة الطلبة
header("Location: manage_students.php");
exit();
?>