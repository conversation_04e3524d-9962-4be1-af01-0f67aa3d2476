<?php
session_start();
include 'db_connect.php';
include 'password_helper.php';

$message = "";
$message_type = "";

// إنشاء رمز CSRF إذا لم يكن موجودًا
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // التحقق من رمز CSRF
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "خطأ في التحقق من الأمان. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.";
        $message_type = "danger";
    } else {
        $username = $conn->real_escape_string($_POST['username']);
        
        // التحقق من وجود المستخدم
        $stmt = $conn->prepare("SELECT id, username FROM residence_managers WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            
            // إنشاء رمز إعادة تعيين
            $reset_token = generate_reset_token();
            $expiry_time = time();
            
            // تخزين الرمز في قاعدة البيانات
            $update_stmt = $conn->prepare("UPDATE residence_managers SET reset_token = ?, reset_token_expiry = ? WHERE id = ?");
            $update_stmt->bind_param("sii", $reset_token, $expiry_time, $user['id']);
            $update_stmt->execute();
            
            // في بيئة الإنتاج: إرسال بريد إلكتروني بالرمز
            // لأغراض التطوير، سنقوم بتخزين الرمز مباشرة في الجلسة
            $_SESSION['reset_email_sent'] = true;
            $_SESSION['reset_username'] = $username;
            
            // تسجيل عملية طلب استعادة كلمة المرور
            log_message("تم طلب استعادة كلمة المرور للمستخدم: " . $username);
            
            $message = "تم إرسال رابط إعادة تعيين كلمة المرور. يرجى التحقق من بريدك الإلكتروني.";
            $message_type = "success";
            
            // توجيه إلى صفحة إعادة تعيين كلمة المرور (في البيئة التطويرية فقط)
            header("Location: reset_password.php?token=" . $reset_token . "&username=" . urlencode($username));
            exit();
        } else {
            $message = "المستخدم غير موجود. يرجى التحقق من اسم المستخدم المدخل.";
            $message_type = "danger";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة كلمة المرور | منصة التحويلات بين الإقامات الجامعية</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
        }
        .forgot-container {
            max-width: 450px;
            margin: 0 auto;
            padding: 15px;
        }
        .forgot-card {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            background-color: white;
            padding: 30px;
            margin-top: 20px;
        }
        .forgot-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .forgot-header img {
            max-width: 80px;
            margin-bottom: 15px;
        }
        .form-floating {
            margin-bottom: 20px;
        }
        .btn-submit {
            width: 100%;
            padding: 12px;
            font-weight: bold;
        }
        .login-link {
            display: block;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container forgot-container">
        <div class="forgot-card">
            <div class="forgot-header">
                <i class="fas fa-lock fa-4x text-primary mb-3"></i>
                <h2>استعادة كلمة المرور</h2>
                <p class="text-muted">أدخل اسم المستخدم لإرسال رابط إعادة تعيين كلمة المرور</p>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php if ($message_type == "danger"): ?>
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php else: ?>
                        <i class="fas fa-check-circle me-2"></i>
                    <?php endif; ?>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <form action="forgot_password.php" method="POST">
                <!-- إضافة حقل مخفي لرمز CSRF -->
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                
                <div class="form-floating mb-4">
                    <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                    <label for="username">اسم المستخدم</label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-submit">
                    <i class="fas fa-paper-plane me-2"></i>
                    إرسال رابط الاستعادة
                </button>
            </form>
            
            <div class="text-center mt-4">
                <a href="admin_login.php" class="text-decoration-none">
                    <i class="fas fa-arrow-right-to-bracket me-1"></i>
                    العودة إلى تسجيل الدخول
                </a>
            </div>
        </div>
    </div>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 