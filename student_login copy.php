<?php
// الاتصال بقاعدة البيانات
include 'db_connect.php';

$message = ""; // متغير لتخزين الرسالة
$message_type = ""; // متغير لتخزين نوع الرسالة

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $baccalaureate_number = $conn->real_escape_string($_POST['baccalaureate_number']);

    // البحث عن الطالب في قاعدة البيانات
    $query = "SELECT * FROM students WHERE baccalaureate_number = '$baccalaureate_number'";
    $result = $conn->query($query);

    // تحقق من وجود الطالب
    if ($result->num_rows > 0) {
        // إذا كان الطالب موجودًا، يمكنك توجيههم إلى صفحة الطلبات
        // على سبيل المثال، يمكنك استخدام session لتخزين معلومات الطالب
        session_start();
        $_SESSION['student'] = $result->fetch_assoc(); // تخزين معلومات الطالب في الجلسة
        header("Location: view_requests.php"); // توجيه إلى صفحة الطلبات
        exit();
    } else {
        // إذا لم يكن موجودًا، يمكنك عرض رسالة خطأ
        $message = "رقم البكالوريا غير موجود. يرجى التحقق من الرقم وإعادة المحاولة.";
        $message_type = "danger";
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دخول الطلبة | منصة التحويلات بين الإقامات الجامعية</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .login-container {
            max-width: 500px;
            margin: 2rem auto;
            padding: 2rem;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header img {
            max-width: 100px;
            margin-bottom: 1rem;
        }
        .login-form {
            margin-bottom: 1.5rem;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .btn-login {
            width: 100%;
            padding: 0.8rem;
            font-size: 1.1rem;
        }
        .login-footer {
            text-align: center;
            margin-top: 1.5rem;
            color: #6c757d;
        }
        .login-footer a {
            color: #0d6efd;
            text-decoration: none;
        }
        .login-footer a:hover {
            text-decoration: underline;
        }
        footer {
            margin-top: auto;
            padding: 1.5rem 0;
        }
        .main-header {
            background-color: #0d6efd;
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- الهيدر الرئيسي -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">منصة التحويلات بين الإقامات الجامعية</h1>
                </div>
                <div class="col-md-6 text-md-end">
                    <nav class="d-inline-block">
                        <a href="index.html" class="btn btn-outline-light">
                            <i class="fas fa-home me-1"></i> الصفحة الرئيسية
                        </a>
                        <a href="admin_login.php" class="btn btn-light ms-2">
                            <i class="fas fa-user-shield me-1"></i> دخول المسؤولين
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="login-container">
            <div class="login-header">
                <div class="mb-4">
                    <i class="fas fa-user-graduate fa-4x text-primary"></i>
                </div>
                <h2 class="mb-3">دخول الطلبة</h2>
                <p class="text-muted">يرجى إدخال رقم البكالوريا للدخول إلى حسابك</p>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form class="login-form" action="student_login.php" method="POST">
                <div class="form-floating mb-4">
                    <input type="text" class="form-control" id="baccalaureate_number" name="baccalaureate_number" placeholder="أدخل رقم البكالوريا" required>
                    <label for="baccalaureate_number">رقم البكالوريا</label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i> دخول
                </button>
            </form>
            
            <div class="login-footer">
                <p class="mb-0">ليس لديك حساب؟ يرجى التواصل مع إدارة الإقامة الجامعية</p>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-white text-center py-3">
        <div class="container">
            <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
        </div>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
