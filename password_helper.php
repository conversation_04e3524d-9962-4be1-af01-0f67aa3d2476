<?php
/**
 * ملف مساعد لوظائف إدارة كلمات المرور بشكل آمن
 */

/**
 * تشفير كلمة المرور باستخدام خوارزمية آمنة
 * 
 * @param string $password كلمة المرور النصية
 * @return string كلمة المرور المشفرة
 */
function hash_password($password) {
    // استخدام خوارزمية PASSWORD_DEFAULT التي تستخدم حالياً bcrypt
    // يمكن للـ PHP في المستقبل تحديث هذه الخوارزمية إلى الأكثر أماناً تلقائياً
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من صحة كلمة المرور
 * 
 * @param string $password كلمة المرور المدخلة
 * @param string $hash كلمة المرور المشفرة المخزنة
 * @return bool صحيح إذا كانت كلمة المرور صحيحة، خطأ خلاف ذلك
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * التحقق مما إذا كانت كلمة المرور بحاجة إلى إعادة تشفير
 * مفيد عند تحديث خوارزميات التشفير
 * 
 * @param string $hash كلمة المرور المشفرة
 * @return bool صحيح إذا كانت بحاجة إلى إعادة تشفير
 */
function check_password_rehash($hash) {
    return password_needs_rehash($hash, PASSWORD_DEFAULT);
}

/**
 * إنشاء كلمة مرور عشوائية آمنة
 * 
 * @param int $length طول كلمة المرور
 * @return string كلمة المرور العشوائية
 */
function generate_random_password($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+;:,.?';
    $password = '';
    $max = strlen($chars) - 1;
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[random_int(0, $max)];
    }
    
    return $password;
}

/**
 * إنشاء رمز إعادة تعيين كلمة المرور
 * 
 * @return string رمز إعادة التعيين
 */
function generate_reset_token() {
    return bin2hex(random_bytes(32));
}

/**
 * إنشاء رمز للمصادقة الثنائية (OTP)
 * 
 * @param int $length طول الرمز
 * @return string رمز المصادقة
 */
function generate_2fa_code($length = 6) {
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= random_int(0, 9);
    }
    return $code;
}

/**
 * التحقق من قوة كلمة المرور
 * 
 * @param string $password كلمة المرور المراد فحصها
 * @return array مصفوفة تحتوي على نتيجة الفحص وملاحظات
 */
function check_password_strength($password) {
    $result = [
        'strong' => false,
        'message' => []
    ];
    
    if (strlen($password) < 8) {
        $result['message'][] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $result['message'][] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $result['message'][] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $result['message'][] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    }
    
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        $result['message'][] = 'كلمة المرور يجب أن تحتوي على حرف خاص واحد على الأقل';
    }
    
    if (empty($result['message'])) {
        $result['strong'] = true;
        $result['message'][] = 'كلمة المرور قوية';
    }
    
    return $result;
}

/**
 * التحقق من صلاحية رمز إعادة تعيين كلمة المرور
 * 
 * @param string $token الرمز
 * @param int $expiry_time وقت انتهاء الصلاحية بالثواني (1 ساعة افتراضياً)
 * @return bool صحيح إذا كان الرمز صالح
 */
function validate_reset_token($token, $timestamp, $expiry_time = 3600) {
    $current_time = time();
    return ($current_time - $timestamp <= $expiry_time);
}
?> 