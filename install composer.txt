# Download Composer installer
curl -sS https://getcomposer.org/installer | php

# Move composer.phar to make it globally accessible
move composer.phar c:\xampp\php\composer.phar

# Create a batch file for easier access
echo @php "c:\xampp\php\composer.phar" %*> c:\xampp\php\composer.bat


# Navigate to your project directory
cd c:\xampp\htdocs\base_transfer

# Initialize Composer in your project (creates composer.json)
composer init --no-interaction --name="your-name/base-transfer" --description="Transfer management system" --type="project" --require="phpoffice/phpspreadsheet:^1.28"

# Install dependencies
composer install