<?php
session_start();
include 'db_connect.php';
include 'password_helper.php';

$message = "";
$message_type = "";

// إنشاء رمز CSRF إذا لم يكن موجودًا
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// التحقق من وجود المعلومات المطلوبة
if (!isset($_GET['token']) || !isset($_GET['username'])) {
    $message = "معلومات غير صالحة. يرجى طلب رابط استعادة جديد.";
    $message_type = "danger";
    // توجيه المستخدم بعد فترة قصيرة
    header("Refresh: 3; URL=forgot_password.php");
} else {
    $token = $_GET['token'];
    $username = $_GET['username'];
    
    // التحقق من صحة الرمز
    $stmt = $conn->prepare("SELECT id, reset_token, reset_token_expiry FROM residence_managers WHERE username = ? AND reset_token = ?");
    $stmt->bind_param("ss", $username, $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows !== 1) {
        $message = "رابط استعادة كلمة المرور غير صالح. يرجى طلب رابط جديد.";
        $message_type = "danger";
        // توجيه المستخدم بعد فترة قصيرة
        header("Refresh: 3; URL=forgot_password.php");
    } else {
        $user = $result->fetch_assoc();
        
        // التحقق من عدم انتهاء صلاحية الرمز (ساعة واحدة)
        if (!validate_reset_token($token, $user['reset_token_expiry'])) {
            $message = "انتهت صلاحية رابط استعادة كلمة المرور. يرجى طلب رابط جديد.";
            $message_type = "danger";
            // توجيه المستخدم بعد فترة قصيرة
            header("Refresh: 3; URL=forgot_password.php");
        }
    }
}

// معالجة النموذج عند تقديمه
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // التحقق من رمز CSRF
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "خطأ في التحقق من الأمان. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.";
        $message_type = "danger";
    } else {
        $username = $_POST['username'];
        $token = $_POST['token'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        // التحقق من تطابق كلمتي المرور
        if ($new_password !== $confirm_password) {
            $message = "كلمتا المرور غير متطابقتين. يرجى المحاولة مرة أخرى.";
            $message_type = "danger";
        } else {
            // فحص قوة كلمة المرور
            $password_check = check_password_strength($new_password);
            
            if (!$password_check['strong']) {
                $message = "كلمة المرور ضعيفة. " . implode(' ', $password_check['message']);
                $message_type = "danger";
            } else {
                // تشفير كلمة المرور الجديدة
                $hashed_password = hash_password($new_password);
                
                // تحديث كلمة المرور وإلغاء رمز إعادة التعيين
                $update_sql = "UPDATE residence_managers SET 
                              password = ?, 
                              reset_token = NULL, 
                              reset_token_expiry = NULL 
                              WHERE username = ? AND reset_token = ?";
                
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("sss", $hashed_password, $username, $token);
                
                if ($stmt->execute()) {
                    // تسجيل عملية إعادة تعيين كلمة المرور
                    if (function_exists('log_message')) {
                       log_message("تم إعادة تعيين كلمة المرور للمستخدم: " . $username);
                    }
                    
                    $message = "تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.";
                    $message_type = "success";
                    
                    // توجيه المستخدم بعد فترة قصيرة
                    header("Refresh: 3; URL=admin_login.php");
                } else {
                    $message = "حدث خطأ أثناء إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى.";
                    $message_type = "danger";
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور | منصة التحويلات بين الإقامات الجامعية</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
        }
        .reset-container {
            max-width: 500px;
            margin: 0 auto;
            padding: 15px;
        }
        .reset-card {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            background-color: white;
            padding: 30px;
            margin-top: 20px;
        }
        .reset-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .form-floating {
            margin-bottom: 20px;
        }
        .btn-reset {
            width: 100%;
            padding: 12px;
            font-weight: bold;
        }
        .password-feedback {
            font-size: 0.85rem;
            margin-top: 0.5rem;
        }
        .strength-meter {
            height: 5px;
            margin-top: 10px;
            background-color: #e9ecef;
            border-radius: 3px;
        }
        .strength-meter div {
            height: 100%;
            border-radius: 3px;
            transition: width 0.5s;
        }
        .password-info {
            margin-top: 10px;
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <i class="fas fa-key fa-4x text-primary mb-3"></i>
                <h2>إعادة تعيين كلمة المرور</h2>
                <p class="text-muted">أدخل كلمة المرور الجديدة</p>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php if ($message_type == "danger"): ?>
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php else: ?>
                        <i class="fas fa-check-circle me-2"></i>
                    <?php endif; ?>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!isset($message) || $message_type !== "danger" || (isset($_GET['token']) && isset($_GET['username']))): ?>
                <form action="reset_password.php" method="POST" id="resetForm">
                    <!-- إضافة حقول مخفية للمعلومات المطلوبة -->
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <input type="hidden" name="token" value="<?php echo isset($_GET['token']) ? $_GET['token'] : ''; ?>">
                    <input type="hidden" name="username" value="<?php echo isset($_GET['username']) ? $_GET['username'] : ''; ?>">
                    
                    <div class="form-floating mb-3">
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               placeholder="كلمة المرور الجديدة" required minlength="8" onkeyup="checkPasswordStrength()">
                        <label for="new_password">كلمة المرور الجديدة</label>
                    </div>
                    
                    <div class="strength-meter">
                        <div id="strength-bar"></div>
                    </div>
                    <div class="password-feedback text-muted" id="password-feedback"></div>
                    
                    <div class="password-info">
                        <p class="mb-1"><i class="fas fa-info-circle me-1"></i> يجب أن تحتوي كلمة المرور على:</p>
                        <ul class="ps-3 mb-0">
                            <li>8 أحرف على الأقل</li>
                            <li>حرف كبير على الأقل</li>
                            <li>حرف صغير على الأقل</li>
                            <li>رقم واحد على الأقل</li>
                            <li>رمز خاص على الأقل</li>
                        </ul>
                    </div>
                    
                    <div class="form-floating mb-4 mt-3">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               placeholder="تأكيد كلمة المرور" required minlength="8" onkeyup="checkPasswordMatch()">
                        <label for="confirm_password">تأكيد كلمة المرور</label>
                        <div class="password-feedback" id="password-match-feedback"></div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-reset" id="resetButton" disabled>
                        <i class="fas fa-save me-2"></i>
                        حفظ كلمة المرور الجديدة
                    </button>
                </form>
            <?php endif; ?>
            
            <div class="text-center mt-4">
                <a href="admin_login.php" class="text-decoration-none">
                    <i class="fas fa-arrow-right-to-bracket me-1"></i>
                    العودة إلى تسجيل الدخول
                </a>
            </div>
        </div>
    </div>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // فحص قوة كلمة المرور
        function checkPasswordStrength() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const strengthBar = document.getElementById('strength-bar');
            const strengthFeedback = document.getElementById('password-feedback');
            const resetButton = document.getElementById('resetButton');

            // إعادة تعيين
            strengthBar.style.width = '0%';
            strengthBar.style.backgroundColor = '#e9ecef';
            strengthFeedback.textContent = '';

            if (password.length === 0) {
                 resetButton.disabled = true;
                 checkPasswordMatch();
                 return;
            }

            // حساب قوة كلمة المرور
            let strength = 0;
            const regexes = [
                /[A-Z]/,
                /[a-z]/,
                /[0-9]/,
                /[^A-Za-z0-9]/
            ];
            strength += Math.min(4, Math.floor(password.length / 2));
            regexes.forEach(regex => {
                if (regex.test(password)) { strength += 1; }
            });

            let percentage = (strength / 8) * 100;
            strengthBar.style.width = percentage + '%';

            let isStrong = false;
            if (strength < 5) {
                strengthBar.style.backgroundColor = strength < 3 ? '#dc3545' : '#ffc107';
                strengthFeedback.textContent = strength < 3 ? 'ضعيفة جداً' : 'ضعيفة';
                strengthFeedback.style.color = strength < 3 ? '#dc3545' : '#ffc107';
                isStrong = false;
            } else {
                 strengthBar.style.backgroundColor = strength < 7 ? '#0dcaf0' : '#198754';
                 strengthFeedback.textContent = strength < 7 ? 'متوسطة' : 'قوية';
                 strengthFeedback.style.color = strength < 7 ? '#0dcaf0' : '#198754';
                 isStrong = true;
            }

            const passwordsMatch = (password === confirmPassword) && (confirmPassword.length > 0);
            resetButton.disabled = !(isStrong && passwordsMatch);
        }

        // التحقق من تطابق كلمتي المرور
        function checkPasswordMatch() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const matchFeedback = document.getElementById('password-match-feedback');
            const strengthFeedback = document.getElementById('password-feedback');
            const resetButton = document.getElementById('resetButton');

            const isStrong = strengthFeedback.textContent === 'متوسطة' || strengthFeedback.textContent === 'قوية';

            if (confirmPassword.length === 0) {
                matchFeedback.textContent = '';
                resetButton.disabled = true;
                return;
            }

            if (password === confirmPassword) {
                matchFeedback.textContent = 'كلمتا المرور متطابقتان';
                matchFeedback.style.color = '#198754';
                resetButton.disabled = !isStrong;
            } else {
                matchFeedback.textContent = 'كلمتا المرور غير متطابقتين';
                matchFeedback.style.color = '#dc3545';
                resetButton.disabled = true;
            }
        }

        document.addEventListener('DOMContentLoaded', checkPasswordStrength);
    </script>
</body>
</html>