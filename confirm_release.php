<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin'])) {
    header("Location: admin_login.php");
    exit();
}

include 'db_connect.php';

// الحصول على معلومات المسؤول
$admin_username = $_SESSION['admin']['username'];
$residence_name = $_SESSION['admin']['residence_name'];

$message = ""; // متغير لتخزين الرسائل

// تأكيد التبرئة
if (isset($_GET['confirm_release'])) {
    $request_id = $_GET['confirm_release'];
    $sql = "UPDATE requests SET release_status = 'yes' WHERE id = $request_id";
    if ($conn->query($sql) === TRUE) {
        $message = "تم تأكيد التبرئة بنجاح";
    } else {
        $message = "خطأ في تأكيد التبرئة: " . $conn->error;
    }
}

// إلغاء التبرئة
if (isset($_GET['cancel_release'])) {
    $request_id = $_GET['cancel_release'];
    $sql = "UPDATE requests SET release_status = 'no' WHERE id = $request_id";
    if ($conn->query($sql) === TRUE) {
        $message = "تم إلغاء التبرئة بنجاح";
    } else {
        $message = "خطأ في إلغاء التبرئة: " . $conn->error;
    }
}

// بناء استعلام البحث
$search_query = "";
if (isset($_GET['search_field']) && isset($_GET['search_value']) && !empty($_GET['search_value'])) {
    $search_field = $_GET['search_field'];
    $search_value = $conn->real_escape_string($_GET['search_value']);
    
    // تحديد الحقل المناسب للبحث
    switch ($search_field) {
        case 'baccalaureate_number':
            $search_query = " AND s.baccalaureate_number LIKE '%$search_value%'";
            break;
        case 'last_name':
            $search_query = " AND s.last_name LIKE '%$search_value%'";
            break;
        case 'first_name':
            $search_query = " AND s.first_name LIKE '%$search_value%'";
            break;
        case 'birth_date':
            $search_query = " AND s.birth_date LIKE '%$search_value%'";
            break;
        case 'birth_place':
            $search_query = " AND s.birth_place LIKE '%$search_value%'";
            break;
        case 'specialization':
            $search_query = " AND s.specialization LIKE '%$search_value%'";
            break;
        case 'study_year':
            $search_query = " AND s.study_year LIKE '%$search_value%'";
            break;
        case 'residence':
            $search_query = " AND s.residence LIKE '%$search_value%'";
            break;
        case 'residence_requested':
            $search_query = " AND r.residence_requested LIKE '%$search_value%'";
            break;
    }
}

// استرجاع جميع الطلبات مع معلومات الطلاب المفصلة
$sql = "SELECT r.id, s.baccalaureate_number, s.last_name, s.first_name, 
        s.birth_date, s.birth_place, s.residence, 
        s.specialization, s.study_year, r.residence_requested, r.status, 
        r.release_status, r.created_at 
        FROM requests r 
        JOIN students s ON r.student_id = s.id 
        WHERE r.status = 'accepted' AND s.residence = '$residence_name' $search_query
        ORDER BY r.created_at DESC";
$result = $conn->query($sql);

// دالة لترجمة حالة التبرئة
function translateReleaseStatus($status) {
    return ($status == 'yes') ? 'تمت التبرئة' : 'لم تتم التبرئة';
}

// دالة لإرجاع لون الشارة حسب حالة التبرئة
function getReleaseStatusBadgeClass($status) {
    return ($status == 'yes') ? 'bg-success' : 'bg-danger';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد التبرئة</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #007bff;
        }
        .main-content {
            padding: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .user-badge {
            background-color: #f8f9fa;
            border-radius: 50px;
            padding: 5px 15px;
            font-size: 0.9rem;
            color: #343a40;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="text-center p-3 mb-3 border-bottom">
                    <h5 class="mb-0">منصة التحويلات</h5>
                    <p class="small mb-0">مسؤول الإقامة</p>
                    <div class="mt-2">
                        <span class="user-badge">
                            <i class="fas fa-user me-1"></i> <?php echo $admin_username; ?>
                        </span>
                    </div>
                    <div class="mt-2">
                        <span class="user-badge">
                            <i class="fas fa-building me-1"></i> <?php echo $residence_name; ?>
                        </span>
                    </div>
                </div>
                <ul class="nav flex-column p-2">
                    <li class="nav-item">
                        <a class="nav-link active rounded" href="confirm_release.php">
                            <i class="fas fa-check-circle me-2"></i>
                            تأكيد التبرئة
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link rounded bg-danger text-white" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تأكيد التبرئة للطلبات المقبولة</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-file-export me-1"></i> تصدير
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-print me-1"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert <?php echo strpos($message, 'تم') !== false ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">بحث متقدم</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="search_field" class="form-label">البحث حسب:</label>
                                    <select name="search_field" id="search_field" class="form-select">
                                        <option value="baccalaureate_number" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'baccalaureate_number') ? 'selected' : ''; ?>>رقم البكالوريا</option>
                                        <option value="last_name" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'last_name') ? 'selected' : ''; ?>>اللقب</option>
                                        <option value="first_name" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'first_name') ? 'selected' : ''; ?>>الاسم</option>
                                        <option value="birth_date" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'birth_date') ? 'selected' : ''; ?>>تاريخ الميلاد</option>
                                        <option value="birth_place" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'birth_place') ? 'selected' : ''; ?>>مكان الميلاد</option>
                                        <option value="specialization" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'specialization') ? 'selected' : ''; ?>>التخصص</option>
                                        <option value="study_year" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'study_year') ? 'selected' : ''; ?>>السنة الدراسية</option>
                                        <option value="residence_requested" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'residence_requested') ? 'selected' : ''; ?>>الإقامة المطلوبة</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="search_value" class="form-label">قيمة البحث:</label>
                                    <input type="text" name="search_value" id="search_value" class="form-control" placeholder="أدخل قيمة البحث" value="<?php echo isset($_GET['search_value']) ? htmlspecialchars($_GET['search_value']) : ''; ?>">
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <div class="d-grid gap-2 w-100">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- الحفاظ على حالة التصفية عند البحث -->
                            <?php if (isset($_GET['release_status']) && $_GET['release_status'] != 'all'): ?>
                                <input type="hidden" name="release_status" value="<?php echo $_GET['release_status']; ?>">
                            <?php endif; ?>
                        </form>
                        
                        <div class="mt-3">
                            <a href="confirm_release.php" class="btn btn-outline-secondary">
                                <i class="fas fa-redo me-1"></i> إعادة ضبط البحث
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">قائمة الطلبات المقبولة</h5>
                            <div class="btn-group" role="group" aria-label="تصفية حسب حالة التبرئة">
                                <a href="?release_status=all<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?>" class="btn btn-sm <?php echo !isset($_GET['release_status']) || $_GET['release_status'] == 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    الكل
                                </a>
                                <a href="?release_status=yes<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?>" class="btn btn-sm <?php echo isset($_GET['release_status']) && $_GET['release_status'] == 'yes' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    تمت التبرئة
                                </a>
                                <a href="?release_status=no<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?>" class="btn btn-sm <?php echo isset($_GET['release_status']) && $_GET['release_status'] == 'no' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                    لم تتم التبرئة
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم البكالوريا</th>
                                        <th>اللقب</th>
                                        <th>الاسم</th>
                                        <th>تاريخ الميلاد</th>
                                        <th>مكان الميلاد</th>
                                        <th>التخصص</th>
                                        <th>السنة</th>
                                        <th>الإقامة المطلوبة</th>
                                        <th>حالة التبرئة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if ($result->num_rows > 0):
                                        while ($row = $result->fetch_assoc()): 
                                            // تصفية حسب حالة التبرئة إذا تم تحديدها
                                            if (isset($_GET['release_status']) && $_GET['release_status'] != 'all' && $row['release_status'] != $_GET['release_status']) {
                                                continue;
                                            }
                                    ?>
                                    <tr>
                                        <td><?php echo $row['baccalaureate_number']; ?></td>
                                        <td><?php echo $row['last_name']; ?></td>
                                        <td><?php echo $row['first_name']; ?></td>
                                        <td><?php 
                                            //date_default_timezone_set('Africa/Casablanca'); 
                                            echo date('Y-m-d', strtotime($row['birth_date'])); 
                                            ?>
                                        </td>
                                        <td><?php echo $row['birth_place']; ?></td>
                                        <td><?php echo $row['specialization']; ?></td>
                                        <td><?php echo $row['study_year']; ?></td>
                                        <td><?php echo $row['residence_requested']; ?></td>
                                        <td>
                                            <span class="badge <?php echo getReleaseStatusBadgeClass($row['release_status']); ?>">
                                                <?php echo translateReleaseStatus($row['release_status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($row['release_status'] == 'no'): ?>
                                                <a href="?confirm_release=<?php echo $row['id']; ?><?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['release_status']) ? '&release_status='.$_GET['release_status'] : ''; ?>" class="btn btn-success btn-sm">
                                                    <i class="fas fa-check me-1"></i> تأكيد التبرئة
                                                </a>
                                            <?php else: ?>
                                                <a href="?cancel_release=<?php echo $row['id']; ?><?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['release_status']) ? '&release_status='.$_GET['release_status'] : ''; ?>" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-times me-1"></i> إلغاء التبرئة
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php 
                                        endwhile; 
                                    else: 
                                    ?>
                                    <tr>
                                        <td colspan="10" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i> لا توجد طلبات مطابقة للبحث
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
$conn->close();
?>
