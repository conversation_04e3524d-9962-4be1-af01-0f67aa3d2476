HTML.BlockWrapper
TYPE: string
VERSION: 1.3.0
DEFAULT: 'p'
--DESCRIPTION--

<p>
    String name of element to wrap inline elements that are inside a block
    context.  This only occurs in the children of blockquote in strict mode.
</p>
<p>
    Example: by default value,
    <code>&lt;blockquote&gt;Foo&lt;/blockquote&gt;</code> would become
    <code>&lt;blockquote&gt;&lt;p&gt;Foo&lt;/p&gt;&lt;/blockquote&gt;</code>.
    The <code>&lt;p&gt;</code> tags can be replaced with whatever you desire,
    as long as it is a block level element.
</p>
--# vim: et sw=4 sts=4
