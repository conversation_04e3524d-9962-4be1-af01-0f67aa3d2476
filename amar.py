# إعادة تحميل المكتبات بعد إعادة التشغيل
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import os

# استخدام خط كوفي (بديل AmiriQuran إذا لم يتوفر كوفي حقيقي)
# تعديل مسار الخط ليناسب نظام Windows
kufi_font_path = "c:\\xampp\\htdocs\\base_transfer\\fonts\\arabtype.ttf"
font_name = "<PERSON><PERSON>"

# تسجيل الخط
try:
    pdfmetrics.registerFont(TTFont(font_name, kufi_font_path))
except:
    # استخدام خط افتراضي إذا لم يتوفر الخط المطلوب
    print("الخط غير متوفر، سيتم استخدام خط افتراضي")
    pdfmetrics.registerFont(TTFont(font_name, "c:\\Windows\\Fonts\\arial.ttf"))
    font_name = "Helvetica"  # استخدام خط متوفر افتراضياً

# الحروف العربية المفردة مع ألف بهمزة
arabic_letters = [
    'أ', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س',
    'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م',
    'ن', 'ه', 'و', 'ي'
]

# إعداد ملف PDF
pdf_path = "c:\\xampp\\htdocs\\base_transfer\\arabic_letters.pdf"
c = canvas.Canvas(pdf_path, pagesize=A4)
width, height = A4

# حجم الخط الكبير
font_size = 400

# تحديد سمك وهامش الإطار
border_width = 2
margin = 20

# رسم كل حرف في صفحة مستقلة
for letter in arabic_letters:
    # رسم الإطار حول الصفحة
    c.setLineWidth(border_width)
    c.rect(margin, margin, width - 2*margin, height - 2*margin)
    
    # رسم الحرف
    c.setFont(font_name, font_size)
    c.drawCentredString(width / 2, height / 2 - (font_size / 3), letter)
    c.showPage()

c.save()
print(f"تم إنشاء ملف PDF بنجاح في: {pdf_path}")