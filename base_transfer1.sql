-- إنشاء قاعدة البيانات
CREATE DATABASE base_transfer;

-- استخدام قاعدة البيانات
USE base_transfer;

-- إنشاء جدول المستخدمين
CREATE TABLE residence_managers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('manager', 'residence_manager') NOT NULL
);

-- إنشاء جدول الطلبة (للاحتفاظ بمعلومات الطلبة)
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    baccalaureate_number VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    residence VARCHAR(100) NOT NULL
);
-- إنشاء جدول الطلبات
CREATE TABLE requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    residence_requested VARCHAR(100) NOT NULL,
    status ENUM('قيد الدراسة', 'مقبول', 'مرفوض') DEFAULT 'قيد الدراسة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
);
