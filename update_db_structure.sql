-- تحديث جدول مدراء الإقامات لدعم استعادة كلمة المرور والأمان المتقدم
ALTER TABLE residence_managers
ADD COLUMN reset_token VARCHAR(255) DEFAULT NULL,
ADD COLUMN reset_token_expiry BIGINT DEFAULT NULL,
ADD COLUMN last_login DATETIME DEFAULT NULL,
ADD COLUMN two_factor_enabled BOOLEAN DEFAULT 0,
ADD COLUMN two_factor_secret VARCHAR(255) DEFAULT NULL;

-- تعديل حقل كلمة المرور ليكون أطول (لدعم التشفير)
ALTER TABLE residence_managers
MODIFY password VARCHAR(255) NOT NULL;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول سجل محاولات الدخول
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    ip_address VARCHAR(50) NOT NULL,
    attempt_time DATETIME NOT NULL,
    success BOOLEAN NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- إنشاء جدول سجل الأنشطة
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    user_type ENUM('admin', 'student') NOT NULL,
    action VARCHAR(255) NOT NULL,
    details TEXT DEFAULT NULL,
    ip_address VARCHAR(50) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    user_type ENUM('admin', 'student') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'danger') DEFAULT 'info',
    link VARCHAR(255) DEFAULT NULL,
    is_read BOOLEAN DEFAULT 0,
    read_at DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8; 