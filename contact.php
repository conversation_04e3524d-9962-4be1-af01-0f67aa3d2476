<?php
session_start();

$message = "";
$message_type = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message_text = trim($_POST['message'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($name) || empty($email) || empty($subject) || empty($message_text)) {
        $message = "يرجى ملء جميع الحقول المطلوبة.";
        $message_type = "danger";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = "يرجى إدخال بريد إلكتروني صحيح.";
        $message_type = "danger";
    } else {
        // في بيئة الإنتاج، هنا سيتم إرسال البريد الإلكتروني
        // لأغراض التطوير، سنقوم فقط بعرض رسالة نجاح
        
        $message = "تم إرسال رسالتك بنجاح! سنقوم بالرد عليك في أقرب وقت ممكن.";
        $message_type = "success";
        
        // إعادة تعيين النموذج
        $_POST = array();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا | منصة التحويلات بين الإقامات الجامعية</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .main-content {
            background: white;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            position: relative;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,100 1000,0 1000,100"/></svg>');
            background-size: cover;
        }

        .content-section {
            padding: 60px 0;
        }

        .contact-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            height: 100%;
            transition: all 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .contact-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .btn-contact {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-contact:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .map-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .contact-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .contact-info-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }

        .contact-info-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-left: 20px;
            width: 60px;
            text-align: center;
        }

        .working-hours {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .working-hours h4 {
            margin-bottom: 20px;
        }

        .working-hours ul {
            list-style: none;
            padding: 0;
        }

        .working-hours li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .working-hours li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h4 mb-0 text-white">
                    <i class="fas fa-phone me-2"></i>
                    اتصل بنا
                </h1>
                <a href="index.html" class="btn btn-outline-light">
                    <i class="fas fa-home me-1"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">اتصل بنا</h1>
            <p class="lead mb-0">نحن هنا لمساعدتك! لا تتردد في التواصل معنا لأي استفسار أو مساعدة</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-section">
            <div class="container">
                <!-- Contact Form and Info -->
                <div class="row">
                    <!-- Contact Form -->
                    <div class="col-lg-8 mb-4">
                        <div class="contact-card">
                            <h3 class="mb-4">
                                <i class="fas fa-paper-plane me-2"></i>
                                أرسل لنا رسالة
                            </h3>
                            
                            <?php if (!empty($message)): ?>
                                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                                    <?php if ($message_type == "danger"): ?>
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-check-circle me-2"></i>
                                    <?php endif; ?>
                                    <?php echo $message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>
                            
                            <form action="contact.php" method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo $_POST['name'] ?? ''; ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo $_POST['email'] ?? ''; ?>" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">الموضوع *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="<?php echo $_POST['subject'] ?? ''; ?>" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="message" class="form-label">الرسالة *</label>
                                    <textarea class="form-control" id="message" name="message" rows="6" 
                                              required><?php echo $_POST['message'] ?? ''; ?></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-contact">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال الرسالة
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="col-lg-4">
                        <div class="contact-card">
                            <h3 class="mb-4">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات التواصل
                            </h3>
                            
                            <div class="contact-info-item">
                                <i class="fas fa-map-marker-alt contact-info-icon"></i>
                                <div>
                                    <h6 class="mb-1">العنوان</h6>
                                    <p class="mb-0">شارع الاستقلال، المسيلة، الجزائر</p>
                                </div>
                            </div>
                            
                            <div class="contact-info-item">
                                <i class="fas fa-phone contact-info-icon"></i>
                                <div>
                                    <h6 class="mb-1">الهاتف</h6>
                                    <p class="mb-0">************</p>
                                </div>
                            </div>
                            
                            <div class="contact-info-item">
                                <i class="fas fa-envelope contact-info-icon"></i>
                                <div>
                                    <h6 class="mb-1">البريد الإلكتروني</h6>
                                    <p class="mb-0"><EMAIL></p>
                                </div>
                            </div>
                            
                            <div class="contact-info-item">
                                <i class="fas fa-fax contact-info-icon"></i>
                                <div>
                                    <h6 class="mb-1">الفاكس</h6>
                                    <p class="mb-0">025-123-4568</p>
                                </div>
                            </div>
                            
                            <!-- Working Hours -->
                            <div class="working-hours">
                                <h4>
                                    <i class="fas fa-clock me-2"></i>
                                    ساعات العمل
                                </h4>
                                <ul>
                                    <li>
                                        <strong>الأحد - الخميس:</strong> 8:00 ص - 4:00 م
                                    </li>
                                    <li>
                                        <strong>الجمعة:</strong> 8:00 ص - 12:00 م
                                    </li>
                                    <li>
                                        <strong>السبت:</strong> مغلق
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Contact Methods -->
                <div class="row mt-5">
                    <div class="col-12">
                        <h3 class="text-center mb-5">
                            <i class="fas fa-headset me-2"></i>
                            طرق التواصل الإضافية
                        </h3>
                    </div>
                    
                    <div class="col-md-4 mb-4">
                        <div class="contact-card text-center">
                            <i class="fas fa-comments contact-icon"></i>
                            <h4>الدردشة المباشرة</h4>
                            <p>تواصل معنا مباشرة عبر الدردشة المباشرة للحصول على إجابات فورية</p>
                            <button class="btn btn-primary" onclick="openChat()">
                                <i class="fas fa-comment me-2"></i>
                                بدء الدردشة
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-4">
                        <div class="contact-card text-center">
                            <i class="fas fa-video contact-icon"></i>
                            <h4>مكالمة فيديو</h4>
                            <p>احجز موعداً لمكالمة فيديو مع أحد ممثلينا للحصول على مساعدة مفصلة</p>
                            <button class="btn btn-primary" onclick="bookVideoCall()">
                                <i class="fas fa-calendar me-2"></i>
                                حجز موعد
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-4">
                        <div class="contact-card text-center">
                            <i class="fas fa-ticket-alt contact-icon"></i>
                            <h4>تذكرة دعم</h4>
                            <p>أنشئ تذكرة دعم فني للحصول على مساعدة متخصصة في المشاكل التقنية</p>
                            <a href="support.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء تذكرة
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Map Section -->
                <div class="row mt-5">
                    <div class="col-12">
                        <h3 class="text-center mb-5">
                            <i class="fas fa-map me-2"></i>
                            موقعنا
                        </h3>
                        <div class="map-container">
                            <iframe 
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3190.1234567890123!2d4.123456789012345!3d35.12345678901234!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzXCsDA3JzM0LjQiTiA0wrAwNyczNC40IkU!5e0!3m2!1sen!2sdz!4v1234567890123"
                                width="100%" 
                                height="400" 
                                style="border:0;" 
                                allowfullscreen="" 
                                loading="lazy" 
                                referrerpolicy="no-referrer-when-downgrade">
                            </iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 منصة التحويلات بين الإقامات الجامعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function openChat() {
            alert('سيتم تفعيل الدردشة المباشرة قريباً. يمكنك التواصل معنا عبر النموذج أعلاه أو الهاتف.');
        }
        
        function bookVideoCall() {
            alert('سيتم تفعيل حجز مواعيد المكالمات المرئية قريباً. يمكنك التواصل معنا عبر النموذج أعلاه أو الهاتف.');
        }
        
        // إعادة تعيين النموذج عند النجاح
        <?php if ($message_type === "success"): ?>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.querySelector('form').reset();
            }, 3000);
        });
        <?php endif; ?>
    </script>
</body>
</html> 