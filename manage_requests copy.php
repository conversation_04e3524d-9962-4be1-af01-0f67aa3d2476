<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

include 'db_connect.php';

// الحصول على معلومات المسؤول
$admin_username = $_SESSION['admin']['username'];
$residence_name = $_SESSION['admin']['residence_name'];

$message = ""; // متغير لتخزين الرسائل
$message_type = ""; // متغير لتخزين نوع الرسالة

// حذف طلب
if (isset($_GET['delete'])) {
    $request_id = $_GET['delete'];
    $sql = "DELETE FROM requests WHERE id = $request_id";
    if ($conn->query($sql) === TRUE) {
        $message = "تم حذف الطلب بنجاح";
        $message_type = "success";
    } else {
        $message = "خطأ في حذف الطلب: " . $conn->error;
        $message_type = "danger";
    }
}

// معالجة قبول/رفض الطلبات
if (isset($_GET['accept'])) {
    $request_id = $_GET['accept'];
    $sql = "UPDATE requests SET status = 'accepted' WHERE id = $request_id";
    if ($conn->query($sql) === TRUE) {
        $message = "تم قبول الطلب بنجاح";
        $message_type = "success";
    } else {
        $message = "خطأ في قبول الطلب: " . $conn->error;
        $message_type = "danger";
    }
} elseif (isset($_GET['reject'])) {
    $request_id = $_GET['reject'];
    $sql = "UPDATE requests SET status = 'rejected' WHERE id = $request_id";
    if ($conn->query($sql) === TRUE) {
        $message = "تم رفض الطلب بنجاح";
        $message_type = "success";
    } else {
        $message = "خطأ في رفض الطلب: " . $conn->error;
        $message_type = "danger";
    }
}

// تغيير حالة طلب إلى قيد الدراسة
if (isset($_GET['pending'])) {
    $request_id = $_GET['pending'];
    $sql = "UPDATE requests SET status = 'pending' WHERE id = $request_id";
    if ($conn->query($sql) === TRUE) {
        $message = "تم تغيير حالة الطلب إلى قيد الدراسة بنجاح";
        $message_type = "success";
    } else {
        $message = "خطأ في تغيير حالة الطلب: " . $conn->error;
        $message_type = "danger";
    }
}

// بناء استعلام البحث
$search_query = "";
if (isset($_GET['search_field']) && isset($_GET['search_value']) && !empty($_GET['search_value'])) {
    $search_field = $_GET['search_field'];
    $search_value = $conn->real_escape_string($_GET['search_value']);
    
    // تحديد الحقل المناسب للبحث
    switch ($search_field) {
        case 'baccalaureate_number':
            $search_query = " WHERE s.baccalaureate_number LIKE '%$search_value%'";
            break;
        case 'last_name':
            $search_query = " WHERE s.last_name LIKE '%$search_value%'";
            break;
        case 'first_name':
            $search_query = " WHERE s.first_name LIKE '%$search_value%'";
            break;
        case 'birth_date':
            $search_query = " WHERE s.birth_date LIKE '%$search_value%'";
            break;
        case 'birth_place':
            $search_query = " WHERE s.birth_place LIKE '%$search_value%'";
            break;
        case 'specialization':
            $search_query = " WHERE s.specialization LIKE '%$search_value%'";
            break;
        case 'study_year':
            $search_query = " WHERE s.study_year LIKE '%$search_value%'";
            break;
        case 'residence':
            $search_query = " WHERE s.residence LIKE '%$search_value%'";
            break;
        case 'residence_requested':
            $search_query = " WHERE r.residence_requested LIKE '%$search_value%'";
            break;
        case 'release_status':
            $search_query = " WHERE r.release_status = '$search_value'";
            break;
    }
}

// استرجاع جميع الطلبات مع معلومات الطلاب المفصلة
$sql = "SELECT r.id, s.baccalaureate_number, s.last_name, s.first_name, 
                        s.birth_date, s.birth_place, s.residence, 
                        s.specialization, s.study_year, r.residence_requested, r.status, 
                        r.release_status, r.created_at 
                        FROM requests r 
                        JOIN students s ON r.student_id = s.id";

// إضافة شرط البحث إذا وجد
if (!empty($search_query)) {
    $sql .= $search_query;
} 

// إضافة الترتيب
$sql .= " ORDER BY r.created_at DESC";

$result = $conn->query($sql);

// دالة لترجمة حالة الطلب
function translateStatus($status) {
    switch($status) {
        case 'pending':
            return 'قيد الدراسة';
        case 'accepted':
            return 'مقبول';
        case 'rejected':
            return 'مرفوض';
        default:
            return $status;
    }
}

// دالة لترجمة حالة التبرئة
function translateReleaseStatus($status) {
    return ($status == 'yes') ? 'تمت التبرئة' : 'لم تتم التبرئة';
}

// دالة لعرض تفاصيل الطالب
function getStudentDetailsModal($student) {
    $html = '<div class="student-details p-3">';
    $html .= '<h3 class="mb-4 border-bottom pb-2">معلومات الطالب المفصلة</h3>';
    $html .= '<div class="row">';
    $html .= '<div class="col-md-6 mb-3"><span class="fw-bold">رقم البكالوريا:</span> ' . $student['baccalaureate_number'] . '</div>';
    $html .= '<div class="col-md-6 mb-3"><span class="fw-bold">الاسم:</span> ' . $student['first_name'] . '</div>';
    $html .= '<div class="col-md-6 mb-3"><span class="fw-bold">اللقب:</span> ' . $student['last_name'] . '</div>';
    $html .= '<div class="col-md-6 mb-3"><span class="fw-bold">تاريخ الميلاد:</span> ' . date('Y-m-d', strtotime($student['birth_date'])) . '</div>';
    $html .= '<div class="col-md-6 mb-3"><span class="fw-bold">مكان الميلاد:</span> ' . $student['birth_place'] . '</div>';
    $html .= '<div class="col-md-6 mb-3"><span class="fw-bold">الإقامة الحالية:</span> ' . $student['residence'] . '</div>';
    $html .= '<div class="col-md-6 mb-3"><span class="fw-bold">التخصص:</span> ' . $student['specialization'] . '</div>';
    $html .= '<div class="col-md-6 mb-3"><span class="fw-bold">السنة الدراسية:</span> ' . $student['study_year'] . '</div>';
    $html .= '</div>';
    $html .= '</div>';
    return $html;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #007bff;
        }
        .main-content {
            padding: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-accepted {
            background-color: #d4edda;
            color: #155724;
        }
        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .release-yes {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .release-no {
            background-color: #f8d7da;
            color: #842029;
        }
        .search-card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .filter-card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .user-badge {
            background-color: #f8f9fa;
            border-radius: 50px;
            padding: 5px 15px;
            font-size: 0.9rem;
            color: #343a40;
            border: 1px solid #dee2e6;
        }
        .action-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            border-radius: 4px;
        }
        .details-btn {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
        }
        .details-btn:hover {
            background-color: #138496;
        }
        .modal-content {
            border-radius: 10px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .modal-header {
            border-bottom: 1px solid #e9ecef;
            background-color: #f8f9fa;
        }
        .modal-footer {
            border-top: 1px solid #e9ecef;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="text-center p-3 mb-3 border-bottom">
                    <h5 class="mb-0">منصة التحويلات</h5>
                    <p class="small mb-0">لوحة تحكم المدير</p>
                    <div class="mt-2">
                        <span class="user-badge">
                            <i class="fas fa-user-shield me-1"></i> <?php echo $admin_username; ?>
                        </span>
                    </div>
                </div>
                <ul class="nav flex-column p-2">
                    <li class="nav-item">
                        <a class="nav-link active rounded" href="manage_requests.php">
                            <i class="fas fa-exchange-alt me-2"></i>
                            إدارة الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_students.php">
                            <i class="fas fa-user-graduate me-2"></i>
                            إدارة الطلبة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_residences.php">
                            <i class="fas fa-building me-2"></i>
                            إدارة الإقامات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="create_account.php">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب مكلف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_accounts.php">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة الحسابات
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link rounded bg-danger text-white" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة طلبات التحويل</h1>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-info text-dark me-2">
                            <i class="fas fa-building me-1"></i> <?php echo $residence_name; ?>
                        </span>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <!-- نموذج البحث -->
                <div class="card search-card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            بحث متقدم
                        </h5>
                    </div>
                    <div class="card-body">
                        <form class="row g-3" method="GET" action="">
                            <div class="col-md-4">
                                <label for="search_field" class="form-label">البحث حسب:</label>
                                <select class="form-select" name="search_field" id="search_field">
                                    <option value="baccalaureate_number" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'baccalaureate_number') ? 'selected' : ''; ?>>رقم البكالوريا</option>
                                    <option value="last_name" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'last_name') ? 'selected' : ''; ?>>اللقب</option>
                                    <option value="first_name" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'first_name') ? 'selected' : ''; ?>>الاسم</option>
                                    <option value="birth_date" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'birth_date') ? 'selected' : ''; ?>>تاريخ الميلاد</option>
                                    <option value="birth_place" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'birth_place') ? 'selected' : ''; ?>>مكان الميلاد</option>
                                    <option value="specialization" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'specialization') ? 'selected' : ''; ?>>التخصص</option>
                                    <option value="study_year" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'study_year') ? 'selected' : ''; ?>>السنة الدراسية</option>
                                    <option value="residence" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'residence') ? 'selected' : ''; ?>>الإقامة الحالية</option>
                                    <option value="residence_requested" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'residence_requested') ? 'selected' : ''; ?>>الإقامة المطلوبة</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="search_value" class="form-label">قيمة البحث:</label>
                                <input type="text" class="form-control" name="search_value" id="search_value" placeholder="أدخل قيمة البحث" value="<?php echo isset($_GET['search_value']) ? htmlspecialchars($_GET['search_value']) : ''; ?>">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <div class="d-grid gap-2 w-100">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i> بحث
                                    </button>
                                </div>
                            </div>
                            
                            <!-- الحفاظ على حالة التصفية عند البحث -->
                            <?php if (isset($_GET['status']) && $_GET['status'] != 'all'): ?>
                                <input type="hidden" name="status" value="<?php echo $_GET['status']; ?>">
                            <?php endif; ?>
                            
                            <?php if (isset($_GET['release_filter']) && $_GET['release_filter'] != 'all'): ?>
                                <input type="hidden" name="release_filter" value="<?php echo $_GET['release_filter']; ?>">
                            <?php endif; ?>
                        </form>
                    </div>
                    <div class="card-footer bg-light">
                        <a href="manage_requests.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-redo me-1"></i> إعادة ضبط البحث والفلاتر
                        </a>
                    </div>
                </div>
                
                <!-- فلاتر الحالة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card filter-card h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-filter me-2"></i>
                                    تصفية حسب حالة الطلب
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="?status=all<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['release_filter']) ? '&release_filter='.$_GET['release_filter'] : ''; ?>" class="btn <?php echo !isset($_GET['status']) || $_GET['status'] == 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                        <i class="fas fa-list me-1"></i> الكل
                                    </a>
                                    <a href="?status=pending<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['release_filter']) ? '&release_filter='.$_GET['release_filter'] : ''; ?>" class="btn <?php echo isset($_GET['status']) && $_GET['status'] == 'pending' ? 'btn-warning' : 'btn-outline-warning'; ?>">
                                        <i class="fas fa-clock me-1"></i> قيد الدراسة
                                    </a>
                                    <a href="?status=accepted<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['release_filter']) ? '&release_filter='.$_GET['release_filter'] : ''; ?>" class="btn <?php echo isset($_GET['status']) && $_GET['status'] == 'accepted' ? 'btn-success' : 'btn-outline-success'; ?>">
                                        <i class="fas fa-check-circle me-1"></i> مقبول
                                    </a>
                                    <a href="?status=rejected<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['release_filter']) ? '&release_filter='.$_GET['release_filter'] : ''; ?>" class="btn <?php echo isset($_GET['status']) && $_GET['status'] == 'rejected' ? 'btn-danger' : 'btn-outline-danger'; ?>">
                                        <i class="fas fa-times-circle me-1"></i> مرفوض
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card filter-card h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-filter me-2"></i>
                                    تصفية حسب حالة التبرئة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="?release_filter=all<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['status']) ? '&status='.$_GET['status'] : ''; ?>" class="btn <?php echo !isset($_GET['release_filter']) || $_GET['release_filter'] == 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                        <i class="fas fa-list me-1"></i> الكل
                                    </a>
                                    <a href="?release_filter=yes<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['status']) ? '&status='.$_GET['status'] : ''; ?>" class="btn <?php echo isset($_GET['release_filter']) && $_GET['release_filter'] == 'yes' ? 'btn-success' : 'btn-outline-success'; ?>">
                                        <i class="fas fa-check me-1"></i> تمت التبرئة
                                    </a>
                                    <a href="?release_filter=no<?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['status']) ? '&status='.$_GET['status'] : ''; ?>" class="btn <?php echo isset($_GET['release_filter']) && $_GET['release_filter'] == 'no' ? 'btn-danger' : 'btn-outline-danger'; ?>">
                                        <i class="fas fa-times me-1"></i> لم تتم التبرئة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- جدول الطلبات -->
                <div class="card">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">قائمة الطلبات</h5>
                        <span class="badge bg-primary">
                            <?php echo $result->num_rows; ?> طلب
                        </span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">رقم البكالوريا</th>
                                        <th scope="col">الاسم واللقب</th>
                                        <th scope="col">التخصص</th>
                                        <th scope="col">السنة</th>
                                        <th scope="col">الإقامة الحالية</th>
                                        <th scope="col">الإقامة المطلوبة</th>
                                        <th scope="col">حالة الطلب</th>
                                        <th scope="col">حالة التبرئة</th>
                                        <th scope="col">تفاصيل</th>
                                        <th scope="col">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if ($result->num_rows > 0):
                                        while ($row = $result->fetch_assoc()): 
                                            // تصفية حسب الحالة إذا تم تحديدها
                                            if (isset($_GET['status']) && $_GET['status'] != 'all' && $row['status'] != $_GET['status']) {
                                                continue;
                                            }
                                            
                                            // تصفية حسب حالة التبرئة إذا تم تحديدها
                                            if (isset($_GET['release_filter']) && $_GET['release_filter'] != 'all' && $row['release_status'] != $_GET['release_filter']) {
                                                continue;
                                            }
                                    ?>
                                    <tr>
                                        <td><?php echo $row['id']; ?></td>
                                        <td><?php echo $row['baccalaureate_number']; ?></td>
                                        <td><?php echo $row['last_name'] . ' ' . $row['first_name']; ?></td>
                                        <td><?php echo $row['specialization']; ?></td>
                                        <td><?php echo $row['study_year']; ?></td>
                                        <td><?php echo $row['residence']; ?></td>
                                        <td><?php echo $row['residence_requested']; ?></td>
                                        <td>
                                            <span class="status-badge status-<?php echo $row['status']; ?>">
                                                <?php echo translateStatus($row['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="status-badge release-<?php echo $row['release_status']; ?>">
                                                <?php echo translateReleaseStatus($row['release_status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#studentModal" onclick="showDetails(<?php echo $row['id']; ?>)">
                                                <i class="fas fa-info-circle"></i>
                                            </button>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                            <?php if ($row['status'] == 'pending'): ?>
                                                    <a href="?accept=<?php echo $row['id']; ?><?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['status']) ? '&status='.$_GET['status'] : ''; ?><?php echo isset($_GET['release_filter']) ? '&release_filter='.$_GET['release_filter'] : ''; ?>" class="btn btn-success btn-sm action-btn">
                                                        <i class="fas fa-check-circle me-1"></i> قبول
                                                    </a>
                                                    <a href="?reject=<?php echo $row['id']; ?><?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['status']) ? '&status='.$_GET['status'] : ''; ?><?php echo isset($_GET['release_filter']) ? '&release_filter='.$_GET['release_filter'] : ''; ?>" class="btn btn-danger btn-sm action-btn">
                                                        <i class="fas fa-times-circle me-1"></i> رفض
                                                    </a>
                                                <?php elseif ($row['status'] == 'accepted' || $row['status'] == 'rejected'): ?>
                                                    <a href="?pending=<?php echo $row['id']; ?><?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['status']) ? '&status='.$_GET['status'] : ''; ?><?php echo isset($_GET['release_filter']) ? '&release_filter='.$_GET['release_filter'] : ''; ?>" class="btn btn-warning btn-sm action-btn">
                                                        <i class="fas fa-clock me-1"></i> إعادة للدراسة
                                                    </a>
                                                <?php endif; ?>
                                                <a href="?delete=<?php echo $row['id']; ?><?php echo isset($_GET['search_field']) ? '&search_field='.$_GET['search_field'].'&search_value='.$_GET['search_value'] : ''; ?><?php echo isset($_GET['status']) ? '&status='.$_GET['status'] : ''; ?><?php echo isset($_GET['release_filter']) ? '&release_filter='.$_GET['release_filter'] : ''; ?>" class="btn btn-secondary btn-sm action-btn" onclick="return confirm('هل أنت متأكد من حذف هذا الطلب؟')">
                                                    <i class="fas fa-trash-alt me-1"></i> حذف
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php 
                                        endwhile; 
                                    else: 
                                    ?>
                                    <tr>
                                        <td colspan="11" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i> لا توجد طلبات مطابقة للبحث
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نافذة منبثقة لعرض تفاصيل الطالب -->
    <div id="studentModal" class="modal fade" tabindex="-1" aria-labelledby="studentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="studentModalLabel">
                        <i class="fas fa-user-graduate me-2"></i>
                        تفاصيل الطالب
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="studentDetails">
                    <!-- سيتم تحميل تفاصيل الطالب هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // دالة لعرض تفاصيل الطالب
        function showDetails(requestId) {
            <?php
            $student_details = [];
            if ($result->num_rows > 0) {
                $result->data_seek(0); // إعادة المؤشر إلى بداية النتائج
                while ($row = $result->fetch_assoc()) {
                    $student_details[$row['id']] = getStudentDetailsModal($row);
                }
            }
            ?>
            
            var studentDetails = <?php echo json_encode($student_details); ?>;
            document.getElementById("studentDetails").innerHTML = studentDetails[requestId] || 
                '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i> لا توجد تفاصيل متاحة</div>';
            
            var studentModal = new bootstrap.Modal(document.getElementById('studentModal'));
            studentModal.show();
        }
    </script>
</body>
</html>

<?php
$conn->close();
?>