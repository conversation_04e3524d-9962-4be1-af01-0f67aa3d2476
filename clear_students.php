<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

// تضمين ملف الاتصال بقاعدة البيانات
include 'db_connect.php';

// التحقق من وجود طلبات مرتبطة بالطلبة
$check_requests = "SELECT COUNT(*) as count FROM requests";
$result_check = $conn->query($check_requests);
$row_check = $result_check->fetch_assoc();

if ($row_check['count'] > 0) {
    // إذا كانت هناك طلبات مرتبطة، لا يمكن حذف الطلبة
    $_SESSION['import_message'] = "لا يمكن مسح جدول الطلبة لأن هناك طلبات مرتبطة بالطلبة. يجب حذف الطلبات أولاً.";
    $_SESSION['import_message_type'] = "danger";
} else {
    // حذف جميع الطلبة
    $sql = "TRUNCATE TABLE students";
    
    if ($conn->query($sql) === TRUE) {
        $_SESSION['import_message'] = "تم مسح جدول الطلبة بنجاح.";
        $_SESSION['import_message_type'] = "success";
    } else {
        $_SESSION['import_message'] = "خطأ في مسح جدول الطلبة: " . $conn->error;
        $_SESSION['import_message_type'] = "danger";
    }
}

// إعادة التوجيه إلى صفحة إدارة الطلبة
header("Location: manage_students.php");
exit();
?>