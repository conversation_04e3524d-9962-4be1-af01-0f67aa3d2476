<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include 'db_connect.php';

// إنشاء رمز CSRF إذا لم يكن موجودًا
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// استعلام لجلب قائمة الإقامات الجامعية
$residences_query = "SELECT * FROM residences ORDER BY name";
$residences_result = $conn->query($residences_query);

$message = ""; // متغير لتخزين الرسالة
$message_type = ""; // متغير لتخزين نوع الرسالة

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // التحقق من رمز CSRF
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "خطأ في التحقق من الأمان. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.";
        $message_type = "danger";
    } else {
        $username = $_POST['username'];
        $password = $_POST['password'];
        $residence_name = $_POST['residence_name']; // إضافة حقل اسم الإقامة
        $role = "residence_manager"; // تعيين الدور مباشرة كمكلف بالإقامة
        
        // التحقق من عدم تكرار اسم المستخدم باستخدام الاستعلامات المُجهزة
        $check_stmt = $conn->prepare("SELECT * FROM residence_managers WHERE username = ?");
        $check_stmt->bind_param("s", $username);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        
        if ($result->num_rows > 0) {
            $message = "خطأ: اسم المستخدم موجود بالفعل، الرجاء اختيار اسم آخر";
            $message_type = "danger";
        } else {
            // تشفير كلمة المرور باستخدام password_hash
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // إدخال البيانات في قاعدة البيانات مع إضافة اسم الإقامة باستخدام الاستعلامات المُجهزة
            $insert_stmt = $conn->prepare("INSERT INTO residence_managers (username, password, role, residence_name) VALUES (?, ?, ?, ?)");
            $insert_stmt->bind_param("ssss", $username, $hashed_password, $role, $residence_name);
            
            if ($insert_stmt->execute()) {
                $message = "تم إنشاء الحساب بنجاح!";
                $message_type = "success";
            } else {
                $message = "خطأ: " . $conn->error;
                $message_type = "danger";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب مكلف بالإقامة</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #007bff;
        }
        .main-content {
            padding: 20px;
        }
        .form-card {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .form-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .password-toggle {
            cursor: pointer;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="text-center p-3 mb-3 border-bottom">
                    <h5 class="mb-0">منصة التحويلات</h5>
                    <p class="small mb-0">لوحة تحكم المدير</p>
                </div>
                <ul class="nav flex-column p-2">
                    
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_requests.php">
                            <i class="fas fa-exchange-alt me-2"></i>
                            إدارة الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_students.php">
                            <i class="fas fa-user-graduate me-2"></i>
                            إدارة الطلبة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_residences.php">
                            <i class="fas fa-building me-2"></i>
                            إدارة الإقامات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active rounded" href="create_account.php">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب مكلف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_accounts.php">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة الحسابات
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link rounded bg-danger text-white" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إنشاء حساب مكلف بالإقامة</h1>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-6">
                        <div class="card form-card mb-4">
                            <div class="card-header py-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-shield me-2"></i>
                                    إضافة مكلف جديد بالإقامة
                                </h5>
                            </div>
                            <div class="card-body p-4">
                                <form action="create_account.php" method="POST">
                                    <!-- إضافة حقل مخفي لرمز CSRF -->
                                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                    
                                    <div class="mb-3">
                                        <label for="username" class="form-label">اسم المستخدم:</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-user"></i>
                                            </span>
                                            <input type="text" class="form-control" id="username" name="username" required>
                                        </div>
                                        <div class="form-text">يجب أن يكون اسم المستخدم فريداً</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="password" class="form-label">كلمة المرور:</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-lock"></i>
                                            </span>
                                            <input type="password" class="form-control" id="password" name="password" required>
                                            <span class="input-group-text password-toggle" onclick="togglePasswordVisibility()">
                                                <i class="fas fa-eye" id="togglePassword"></i>
                                            </span>
                                        </div>
                                        <div class="form-text">استخدم كلمة مرور قوية تحتوي على أحرف وأرقام ورموز</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="residence_name" class="form-label">اسم الإقامة الجامعية:</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-building"></i>
                                            </span>
                                            <select class="form-select" id="residence_name" name="residence_name" required>
                                                <option value="">-- اختر الإقامة الجامعية --</option>
                                                <?php 
                                                if ($residences_result && $residences_result->num_rows > 0) {
                                                    while($residence = $residences_result->fetch_assoc()) {
                                                        echo '<option value="' . $residence['name'] . '">' . $residence['name'] . ' (' . $residence['gender'] . ')</option>';
                                                    }
                                                } else {
                                                    echo '<option value="" disabled>لا توجد إقامات مسجلة</option>';
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <input type="hidden" name="role" value="residence_manager">
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-user-plus me-2"></i>
                                            إنشاء حساب مكلف بالإقامة
                                        </button>
                                        <a href="manage_accounts.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-users me-2"></i>
                                            عرض جميع الحسابات
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- سكريبت لإظهار/إخفاء كلمة المرور -->
    <script>
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('togglePassword');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>

<?php
$conn->close();
?>
