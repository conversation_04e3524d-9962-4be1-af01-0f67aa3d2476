-- SQL script to add the last_login column to the residence_managers table
-- This can be imported directly in phpMyAdmin

-- Add last_login column if it doesn't exist
ALTER TABLE `residence_managers` 
ADD COLUMN IF NOT EXISTS `last_login` DATETIME NULL COMMENT 'Last login timestamp';

-- Display message (for MySQL clients that support it)
SELECT 'Column last_login has been successfully added to residence_managers table' AS Result; 