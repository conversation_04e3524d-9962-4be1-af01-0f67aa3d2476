<?php
include 'db_connect.php';

$result = $conn->query("SELECT * FROM notifications");
while ($row = $result->fetch_assoc()) {
    echo "ID: " . $row['id'] . 
         ", User ID: " . $row['user_id'] . 
         ", User Type: " . $row['user_type'] . 
         ", Title: " . $row['title'] . 
         ", Is Read: " . ($row['is_read'] ? 'Sí' : 'No') . 
         ", Created: " . $row['created_at'] . 
         "\n";
}

if ($result->num_rows == 0) {
    echo "No hay notificaciones en la base de datos.\n";
}
?> 