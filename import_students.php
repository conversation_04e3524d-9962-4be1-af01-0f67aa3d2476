<?php
session_start();

// *** إضافة: تفعيل عرض الأخطاء وزيادة حدود الموارد وتسجيل التقدم ***
error_reporting(E_ALL); // الإبلاغ عن جميع أنواع الأخطاء
ini_set('display_errors', 1); // عرض الأخطاء على الشاشة (للتصحيح)
ini_set('max_execution_time', 300); // زيادة وقت التنفيذ إلى 300 ثانية (5 دقائق)
ini_set('memory_limit', '256M'); // زيادة حد الذاكرة إلى 256 ميجابايت
$log_file = 'import_log.txt'; // اسم ملف السجل
file_put_contents($log_file, "بدء عملية الاستيراد: " . date('Y-m-d H:i:s') . "\\n", FILE_APPEND);
// *** نهاية الإضافة ***

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

// تضمين ملف الاتصال بقاعدة البيانات
include 'db_connect.php';

// تحميل مكتبة PhpSpreadsheet
require 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

$message = "";
$message_type = "";
$imported_count = 0;
$errors = [];

// التحقق من وجود ملف تم رفعه
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_FILES["excelFile"]) && $_FILES["excelFile"]["error"] == 0) {
    
    // التحقق من نوع الملف
    $allowed_extensions = ['xls', 'xlsx'];
    $file_extension = pathinfo($_FILES["excelFile"]["name"], PATHINFO_EXTENSION);
    
    if (in_array(strtolower($file_extension), $allowed_extensions)) {
        try {
            // قراءة ملف Excel
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($_FILES["excelFile"]["tmp_name"]);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            // تخطي الصف الأول (عناوين الأعمدة)
            $header = array_shift($rows);
            
            // استرجاع قائمة الإقامات الجامعية
            $residences_query = "SELECT * FROM residences";
            $residences_result = $conn->query($residences_query);
            $residences = [];
            
            if ($residences_result && $residences_result->num_rows > 0) {
                while ($residence = $residences_result->fetch_assoc()) {
                    $residences[$residence['name']] = $residence['gender'];
                }
            }
            
            // معالجة كل صف من البيانات
            foreach ($rows as $index => $row) {
                // *** إضافة: تسجيل رقم الصف الحالي ***
                file_put_contents($log_file, "معالجة الصف رقم: " . ($index + 2) . "\\n", FILE_APPEND);
                // *** نهاية الإضافة ***

                // تخطي الصفوف الفارغة
                if (empty($row[4]) && empty($row[5])) {
                    // *** إضافة: تسجيل تخطي الصف ***
                    file_put_contents($log_file, "  -> تم تخطي الصف (فارغ).\\n", FILE_APPEND);
                    // *** نهاية الإضافة ***
                    continue;
                }
                
                // استخراج البيانات من الصف بترتيب حقول جدول الطلبة حسب الترتيب الجديد
                // رقم البكالوريا: العمود 5+العمود4
                $baccalaureate_number = trim($row[4] . $row[3]);
                // اللقب: العمود 10
                $last_name = trim($row[9]);
                // الاسم: العمود 11
                $first_name = trim($row[10]);
                // تاريخ الميلاد: العمود 13
                $birth_date = trim($row[12]);
                // مكان الميلاد: العمود 20
                $birth_place = trim($row[19]);
                // الجنس: اتركه فارغا
                $gender = "";
                // الإقامة: العمود 33
                $residence = trim($row[32]);

                // *** إضافة: تحويل أسماء الإقامات حسب القواعد المحددة ***
                switch (strtolower($residence)) { // استخدام strtolower للمقارنة غير الحساسة لحالة الأحرف
                    case 'ru ben boulaid mohamed   -msila':
                        $residence = 'بن بولعيد محمد';
                        break;
                    case 'ru debih abdelkader   -msila':
                        $residence = 'ذبيح عبد القادر';
                        break;
                    case 'ru hassouni ramdane   -msila':
                        $residence = 'حسوني رمضان';
                        break;
                    case 'ru nouiouet moussa el-ahmadi   -msila':
                        $residence = 'نويوات موسى الأحمدي';
                        break;
                    case 'ru 500 lits  ex-cfa  -msila':
                        $residence = '500 سرير-CFA';
                        break;
                    case 'ru 500 lits  boussaada  -msila':
                        $residence = '500 سرير بوسعادة';
                        break;
                    // يمكنك إضافة حالات أخرى هنا إذا لزم الأمر
                }
                // *** نهاية الإضافة ***

                // التخصص: العمود 22
                $specialization = trim($row[21]);
                // السنة الدراسية: العمود 24
                $study_year = trim($row[23]);
                
                // التحقق من وجود قيمة جنس محددة في النموذج
                if (isset($_POST['importGender']) && !empty($_POST['importGender'])) {
                    // استخدام قيمة الجنس المحددة في النموذج
                    $gender = $_POST['importGender'];
                } else {
                    // استخدام قيمة الجنس من ملف Excel (إذا كانت متوفرة)
                    $gender = "";
                }
                
                // التحقق من صحة البيانات الأساسية
                if (empty($baccalaureate_number) || empty($last_name) || empty($first_name)) {
                    $current_error = "الصف " . ($index + 2) . ": البيانات الأساسية غير مكتملة (رقم البكالوريا، اللقب، الاسم).";
                    $errors[] = $current_error;
                    // *** إضافة: تسجيل الخطأ ***
                    file_put_contents($log_file, "  -> خطأ: " . $current_error . "\\n", FILE_APPEND);
                    // *** نهاية الإضافة ***
                    continue;
                }
                
                // تنسيق تاريخ الميلاد
                if (!empty($birth_date)) {
                    // محاولة تحويل التاريخ إلى التنسيق المطلوب
                    if (is_numeric($birth_date)) {
                        // إذا كان التاريخ بتنسيق Excel الرقمي
                        $unix_date = ($birth_date - 25569) * 86400;
                        $birth_date = date('Y-m-d', $unix_date);
                    } else {
                        // محاولة تحليل التاريخ بتنسيقات مختلفة
                        $date_formats = ['d/m/Y', 'Y-m-d', 'd-m-Y', 'Y/m/d', 'm/d/Y', 'd.m.Y'];
                        $valid_date = false;
                        
                        foreach ($date_formats as $format) {
                            $date_obj = date_create_from_format($format, $birth_date);
                            if ($date_obj) {
                                $birth_date = date_format($date_obj, 'Y-m-d');
                                $valid_date = true;
                                break;
                            }
                        }
                        
                        if (!$valid_date && !is_null($birth_date)) { // تعديل الشرط ليشمل الحالة التي يكون فيها التاريخ غير صالح ولكنه ليس فارغًا
                            // محاولة أخيرة: تحويل النص إلى تاريخ باستخدام strtotime
                            $timestamp = strtotime($birth_date);
                            if ($timestamp === false) {
                                $current_error = "الصف " . ($index + 2) . ": تنسيق تاريخ الميلاد غير صحيح: '$birth_date'.";
                                $errors[] = $current_error;
                                // *** إضافة: تسجيل خطأ التاريخ ***
                                file_put_contents($log_file, "  -> خطأ: " . $current_error . "\\n", FILE_APPEND);
                                // *** نهاية الإضافة ***
                                continue;
                            }
                        }
                    }
                } else {
                    $birth_date = null;
                }
                
                // إزالة التحقق من تطابق جنس الطالب مع جنس الإقامة
                // سيتم استيراد جميع الطلبة بغض النظر عن الجنس
                
                // التحقق من عدم وجود طالب بنفس رقم البكالوريا
                $check_stmt = $conn->prepare("SELECT id FROM students WHERE baccalaureate_number = ?");
                $check_stmt->bind_param("s", $baccalaureate_number);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows > 0) {
                    // تحديث بيانات الطالب الموجود
                    $student = $check_result->fetch_assoc();
                    $student_id = $student['id'];
                    
                    $update_stmt = $conn->prepare("UPDATE students SET 
                        last_name = ?, 
                        first_name = ?, 
                        birth_date = ?, 
                        birth_place = ?, 
                        gender = ?, 
                        residence = ?, 
                        specialization = ?, 
                        study_year = ? 
                        WHERE id = ?");
                    
                    $update_stmt->bind_param("ssssssssi", 
                        $last_name, 
                        $first_name, 
                        $birth_date, 
                        $birth_place, 
                        $gender, 
                        $residence, 
                        $specialization, 
                        $study_year, 
                        $student_id
                    );
                    
                    if ($update_stmt->execute()) {
                        $imported_count++;
                        // *** إضافة: تسجيل التحديث ***
                        file_put_contents($log_file, "  -> تم تحديث الطالب (ID: $student_id).\\n", FILE_APPEND);
                        // *** نهاية الإضافة ***
                    } else {
                        $current_error = "الصف " . ($index + 2) . ": خطأ في تحديث بيانات الطالب: " . $conn->error;
                        $errors[] = $current_error;
                        // *** إضافة: تسجيل خطأ التحديث ***
                        file_put_contents($log_file, "  -> خطأ: " . $current_error . "\\n", FILE_APPEND);
                        // *** نهاية الإضافة ***
                    }
                } else {
                    // إضافة طالب جديد
                    $insert_stmt = $conn->prepare("INSERT INTO students 
                        (baccalaureate_number, last_name, first_name, birth_date, birth_place, gender, residence, specialization, study_year) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    
                    $insert_stmt->bind_param("sssssssss", 
                        $baccalaureate_number, 
                        $last_name, 
                        $first_name, 
                        $birth_date, 
                        $birth_place, 
                        $gender, 
                        $residence, 
                        $specialization, 
                        $study_year
                    );
                    
                    if ($insert_stmt->execute()) {
                        $imported_count++;
                        // *** إضافة: تسجيل الإضافة ***
                        file_put_contents($log_file, "  -> تم إضافة طالب جديد (رقم البكالوريا: $baccalaureate_number).\\n", FILE_APPEND);
                        // *** نهاية الإضافة ***
                    } else {
                        $current_error = "الصف " . ($index + 2) . ": خطأ في إضافة الطالب: " . $conn->error;
                        $errors[] = $current_error;
                        // *** إضافة: تسجيل خطأ الإضافة ***
                        file_put_contents($log_file, "  -> خطأ: " . $current_error . "\\n", FILE_APPEND);
                        // *** نهاية الإضافة ***
                    }
                }
            }
            
            // *** إضافة: تسجيل نهاية المعالجة ***
            file_put_contents($log_file, "انتهاء معالجة الصفوف. إجمالي المستورد: $imported_count. الأخطاء: " . count($errors) . "\\n", FILE_APPEND);
            // *** نهاية الإضافة ***

            // إعداد رسالة النجاح
            if ($imported_count > 0) {
                $message = "تم استيراد $imported_count طالب بنجاح.";
                $message_type = "success";
            } else {
                $message = "لم يتم استيراد أي طالب.";
                $message_type = "warning";
            }
            
        } catch (Exception $e) {
            // *** إضافة: تسجيل الاستثناء ***
            $error_message = "حدث خطأ (استثناء) أثناء معالجة الملف: " . $e->getMessage();
            file_put_contents($log_file, "!!! خطأ فادح: " . $error_message . "\\n", FILE_APPEND);
            // *** نهاية الإضافة ***
            $message = $error_message;
            $message_type = "danger";
        }
    } else {
        // *** إضافة: تسجيل خطأ نوع الملف ***
        file_put_contents($log_file, "!!! خطأ: نوع الملف غير مدعوم.\\n", FILE_APPEND);
        // *** نهاية الإضافة ***
        $message = "نوع الملف غير مدعوم. يرجى استخدام ملفات Excel (.xls أو .xlsx).";
        $message_type = "danger";
    }
} else {
    // *** إضافة: تسجيل خطأ رفع الملف ***
    if (!isset($_FILES["excelFile"])) {
        file_put_contents($log_file, "!!! خطأ: لم يتم تحديد ملف.\\n", FILE_APPEND);
    } elseif ($_FILES["excelFile"]["error"] != 0) {
        file_put_contents($log_file, "!!! خطأ: خطأ في رفع الملف (الكود: " . $_FILES["excelFile"]["error"] . ").\\n", FILE_APPEND);
    }
    // *** نهاية الإضافة ***
    $message = "لم يتم تحديد ملف أو حدث خطأ أثناء رفع الملف.";
    $message_type = "danger";
}

// *** إضافة: تسجيل نهاية السكربت قبل إعادة التوجيه ***
file_put_contents($log_file, "نهاية السكربت. إعادة التوجيه إلى manage_students.php\\n====================\\n", FILE_APPEND);
// *** نهاية الإضافة ***

// تخزين الرسالة في الجلسة لعرضها بعد إعادة التوجيه
$_SESSION['import_message'] = $message;
$_SESSION['import_message_type'] = $message_type;
$_SESSION['import_errors'] = $errors;
$_SESSION['imported_count'] = $imported_count;

// إعادة التوجيه إلى صفحة إدارة الطلبة
header("Location: manage_students.php");
exit();
?>