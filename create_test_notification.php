<?php
include 'db_connect.php';
include 'notifications.php';

// Obtener un ID de administrador
$admin_result = $conn->query("SELECT id FROM residence_managers LIMIT 1");
$admin_id = 0;
if ($admin_result->num_rows > 0) {
    $admin = $admin_result->fetch_assoc();
    $admin_id = $admin['id'];
    
    // Agregar una notificación de prueba
    $notification_id = add_notification(
        $admin_id,
        'admin',
        'إشعار تجريبي',
        'هذه رسالة إشعار تجريبية للتأكد من عمل نظام الإشعارات بشكل صحيح.',
        'info',
        'dashboard.php'
    );
    
    if ($notification_id) {
        echo "تم إنشاء إشعار تجريبي بنجاح برقم: " . $notification_id . "\n";
    } else {
        echo "فشل إنشاء الإشعار التجريبي.\n";
    }
} else {
    echo "لم يتم العثور على أي مسؤول في قاعدة البيانات.\n";
}
?> 