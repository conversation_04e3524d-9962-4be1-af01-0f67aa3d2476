<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

// الاتصال بقاعدة البيانات
include 'db_connect.php';

$message = ""; // متغير لتخزين الرسالة
$message_type = ""; // متغير لتخزين نوع الرسالة

// معالجة إضافة إقامة جديدة
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'add') {
    $name = $conn->real_escape_string($_POST['name']);
    $capacity = intval($_POST['capacity']);
    $current_residents = intval($_POST['current_residents']);
    $gender = $conn->real_escape_string($_POST['gender']);
    $address = $conn->real_escape_string($_POST['address']);
    
    // التحقق من عدم تكرار اسم الإقامة
    $check_sql = "SELECT * FROM residences WHERE name = '$name'";
    $result = $conn->query($check_sql);
    
    if ($result->num_rows > 0) {
        $message = "خطأ: اسم الإقامة موجود بالفعل";
        $message_type = "danger";
    } else {
        $sql = "INSERT INTO residences (name, capacity, current_residents, gender, address) 
                VALUES ('$name', $capacity, $current_residents, '$gender', '$address')";
        
        if ($conn->query($sql) === TRUE) {
            $message = "تم إضافة الإقامة بنجاح";
            $message_type = "success";
        } else {
            $message = "خطأ: " . $conn->error;
            $message_type = "danger";
        }
    }
}

// معالجة تحديث إقامة
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'edit') {
    $id = intval($_POST['id']);
    $name = $conn->real_escape_string($_POST['name']);
    $capacity = intval($_POST['capacity']);
    $current_residents = intval($_POST['current_residents']);
    $gender = $conn->real_escape_string($_POST['gender']);
    $address = $conn->real_escape_string($_POST['address']);
    
    $sql = "UPDATE residences SET 
            name = '$name', 
            capacity = $capacity, 
            current_residents = $current_residents, 
            gender = '$gender', 
            address = '$address' 
            WHERE id = $id";
    
    if ($conn->query($sql) === TRUE) {
        $message = "تم تحديث الإقامة بنجاح";
        $message_type = "success";
    } else {
        $message = "خطأ: " . $conn->error;
        $message_type = "danger";
    }
}

// معالجة حذف إقامة
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    
    // التحقق من عدم وجود مكلفين بهذه الإقامة
    $check_managers = "SELECT * FROM residence_managers WHERE residence_name = (SELECT name FROM residences WHERE id = $id)";
    $managers_result = $conn->query($check_managers);
    
    if ($managers_result->num_rows > 0) {
        $message = "لا يمكن حذف الإقامة لأنها مرتبطة بمكلفين";
        $message_type = "warning";
    } else {
        $sql = "DELETE FROM residences WHERE id = $id";
        
        if ($conn->query($sql) === TRUE) {
            $message = "تم حذف الإقامة بنجاح";
            $message_type = "success";
        } else {
            $message = "خطأ: " . $conn->error;
            $message_type = "danger";
        }
    }
}

// جلب قائمة الإقامات
$residences_query = "SELECT * FROM residences ORDER BY name";
$residences_result = $conn->query($residences_query);

// الحصول على معلومات المسؤول
$admin_username = $_SESSION['admin']['username'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإقامات الجامعية</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #007bff;
        }
        .main-content {
            padding: 20px;
        }
        
        /* تحسينات الشريط الجانبي */
        .sidebar {
            background-color: #343a40;
            min-height: 100vh;
            color: white;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.2rem 0.5rem;
            border-radius: 5px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(-3px);
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .sidebar .nav-link i {
            margin-left: 8px;
            width: 20px;
            text-align: center;
        }
        .sidebar-header {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar-section {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar-heading {
            font-size: 0.85rem;
            text-transform: uppercase;
            padding: 1rem;
            opacity: 0.6;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .form-card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .form-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .residence-table {
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }
        .capacity-badge {
            font-size: 0.85rem;
            padding: 5px 10px;
            border-radius: 50px;
        }
        .gender-badge {
            font-size: 0.85rem;
            padding: 5px 10px;
            border-radius: 50px;
        }
        .gender-male {
            background-color: #cce5ff;
            color: #004085;
        }
        .gender-female {
            background-color: #f8d7da;
            color: #721c24;
        }
        .user-badge {
            background-color: #f8f9fa;
            border-radius: 50px;
            padding: 5px 15px;
            font-size: 0.9rem;
            color: #343a40;
            border: 1px solid #dee2e6;
        }
        .capacity-progress {
            height: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="sidebar-header text-center">
                    <i class="fas fa-university fa-3x mb-3 text-primary"></i>
                    <h5 class="fw-bold">منصة التحويلات</h5>
                    <div class="small opacity-75 mb-3">لوحة تحكم المدير</div>
                    <div class="user-badge d-inline-block">
                        <i class="fas fa-user-shield me-1"></i> <?php echo $admin_username; ?>
                    </div>
                </div>
                
                <div class="px-2">
                    <div class="sidebar-heading">القائمة الرئيسية</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_requests.php">
                                <i class="fas fa-exchange-alt"></i>
                                إدارة الطلبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_students.php">
                                <i class="fas fa-user-graduate"></i>
                                إدارة الطلبة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="manage_residences.php">
                                <i class="fas fa-building"></i>
                                إدارة الإقامات
                            </a>
                        </li>
                    </ul>
                    
                    <div class="sidebar-heading">إدارة النظام</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="create_account.php">
                                <i class="fas fa-user-plus"></i>
                                إنشاء حساب مكلف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_accounts.php">
                                <i class="fas fa-users-cog"></i>
                                إدارة الحسابات
                            </a>
                        </li>
                    </ul>
                    
                    <div class="sidebar-section">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link text-danger" href="logout.php">
                                    <i class="fas fa-sign-out-alt"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة الإقامات الجامعية</h1>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : ($message_type == 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <!-- نموذج إضافة إقامة جديدة -->
                <div class="card form-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            إضافة إقامة جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="manage_residences.php" method="POST" class="row g-3">
                            <input type="hidden" name="action" value="add">
                            <div class="col-md-6">
                                <label for="name" class="form-label">اسم الإقامة:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-building"></i></span>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="capacity" class="form-label">الطاقة النظرية:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-bed"></i></span>
                                    <input type="number" class="form-control" id="capacity" name="capacity" required min="1">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="current_residents" class="form-label">عدد الطلبة المقيمين:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-users"></i></span>
                                    <input type="number" class="form-control" id="current_residents" name="current_residents" required min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="gender" class="form-label">الجنس:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value="" selected disabled>اختر الجنس</option>
                                        <option value="ذكور">ذكور</option>
                                        <option value="اناث">اناث</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="address" class="form-label">العنوان:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                    <textarea class="form-control" id="address" name="address" required rows="1"></textarea>
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة إقامة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- جدول الإقامات -->
                <div class="card residence-table">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الإقامات الجامعية
                        </h5>
                        <span class="badge bg-primary">
                            <?php echo $residences_result->num_rows; ?> إقامة
                        </span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">اسم الإقامة</th>
                                        <th scope="col">الطاقة النظرية</th>
                                        <th scope="col">عدد المقيمين</th>
                                        <th scope="col">نسبة الإشغال</th>
                                        <th scope="col">الجنس</th>
                                        <th scope="col">العنوان</th>
                                        <th scope="col">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if ($residences_result && $residences_result->num_rows > 0) {
                                        while($residence = $residences_result->fetch_assoc()) {
                                            // حساب نسبة الإشغال
                                            $occupancy_rate = ($residence['capacity'] > 0) ? round(($residence['current_residents'] / $residence['capacity']) * 100) : 0;
                                            $progress_class = 'bg-success';
                                            if ($occupancy_rate > 90) {
                                                $progress_class = 'bg-danger';
                                            } elseif ($occupancy_rate > 75) {
                                                $progress_class = 'bg-warning';
                                            }
                                    ?>
                                    <tr>
                                        <td><?php echo $residence['id']; ?></td>
                                        <td>
                                            <strong><?php echo $residence['name']; ?></strong>
                                        </td>
                                        <td>
                                            <span class="capacity-badge bg-light text-dark">
                                                <i class="fas fa-bed me-1"></i> <?php echo $residence['capacity']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="capacity-badge bg-light text-dark">
                                                <i class="fas fa-users me-1"></i> <?php echo $residence['current_residents']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress capacity-progress flex-grow-1 me-2">
                                                    <div class="progress-bar <?php echo $progress_class; ?>" role="progressbar" style="width: <?php echo $occupancy_rate; ?>%" aria-valuenow="<?php echo $occupancy_rate; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                                <span class="small"><?php echo $occupancy_rate; ?>%</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="gender-badge <?php echo $residence['gender'] == 'ذكور' ? 'gender-male' : 'gender-female'; ?>">
                                                <i class="fas fa-<?php echo $residence['gender'] == 'ذكور' ? 'male' : 'female'; ?> me-1"></i>
                                                <?php echo $residence['gender']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo $residence['address']; ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-warning" onclick="editResidence(<?php echo $residence['id']; ?>, '<?php echo $residence['name']; ?>', <?php echo $residence['capacity']; ?>, <?php echo $residence['current_residents']; ?>, '<?php echo $residence['gender']; ?>', '<?php echo addslashes($residence['address']); ?>')">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </button>
                                                <a href="manage_residences.php?delete=<?php echo $residence['id']; ?>" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الإقامة؟')">
                                                    <i class="fas fa-trash-alt"></i> حذف
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php 
                                        }
                                    } else {
                                    ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i> لا توجد إقامات مسجلة
                                            </div>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- نموذج تعديل الإقامة (مخفي بشكل افتراضي) -->
                <div class="modal fade" id="editResidenceModal" tabindex="-1" aria-labelledby="editResidenceModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="editResidenceModalLabel">
                                    <i class="fas fa-edit me-2"></i>
                                    تعديل بيانات الإقامة
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form action="manage_residences.php" method="POST" class="row g-3">
                                    <input type="hidden" name="action" value="edit">
                                    <input type="hidden" id="edit_id" name="id">
                                    <div class="col-md-6">
                                        <label for="edit_name" class="form-label">اسم الإقامة:</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                                            <input type="text" class="form-control" id="edit_name" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="edit_capacity" class="form-label">الطاقة النظرية:</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-bed"></i></span>
                                            <input type="number" class="form-control" id="edit_capacity" name="capacity" required min="1">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="edit_current_residents" class="form-label">عدد الطلبة المقيمين:</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-users"></i></span>
                                            <input type="number" class="form-control" id="edit_current_residents" name="current_residents" required min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="edit_gender" class="form-label">الجنس:</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                                            <select class="form-select" id="edit_gender" name="gender" required>
                                                <option value="ذكور">ذكور</option>
                                                <option value="اناث">اناث</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="edit_address" class="form-label">العنوان:</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                            <textarea class="form-control" id="edit_address" name="address" required rows="2"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-12 mt-4">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i> حفظ التغييرات
                                        </button>
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                            <i class="fas fa-times me-1"></i> إلغاء
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // دالة لتعديل الإقامة
        function editResidence(id, name, capacity, current_residents, gender, address) {
            document.getElementById('edit_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_capacity').value = capacity;
            document.getElementById('edit_current_residents').value = current_residents;
            document.getElementById('edit_gender').value = gender;
            document.getElementById('edit_address').value = address;
            
            // عرض النافذة المنبثقة
            var editModal = new bootstrap.Modal(document.getElementById('editResidenceModal'));
            editModal.show();
        }
    </script>
</body>
</html>

<?php
$conn->close();
?>