<?php
include 'db_connect.php';

// Verificar si la tabla existe
$result = $conn->query("SHOW TABLES LIKE 'notifications'");
if ($result->num_rows > 0) {
    echo "La tabla de notificaciones existe.\n";
    
    // Verificar la estructura
    $result = $conn->query("DESCRIBE notifications");
    echo "Estructura de la tabla:\n";
    while ($row = $result->fetch_assoc()) {
        echo $row['Field'] . " - " . $row['Type'] . " " . $row['Null'] . " " . $row['Default'] . "\n";
    }
    
    // Verificar si hay datos
    $result = $conn->query("SELECT COUNT(*) as count FROM notifications");
    $row = $result->fetch_assoc();
    echo "Número de notificaciones: " . $row['count'] . "\n";
} else {
    echo "La tabla de notificaciones NO existe.\n";
    
    // Intentar crear la tabla
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_type ENUM('admin', 'student') NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'danger') DEFAULT 'info',
        link VARCHAR(255) DEFAULT NULL,
        is_read BOOLEAN DEFAULT 0,
        read_at DATETIME DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8";
    
    if ($conn->query($sql) === TRUE) {
        echo "La tabla de notificaciones ha sido creada correctamente.\n";
    } else {
        echo "Error al crear la tabla: " . $conn->error . "\n";
    }
}
?> 