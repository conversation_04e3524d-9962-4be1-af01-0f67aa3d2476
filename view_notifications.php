<?php
session_start();
require_once 'config.php';
include 'db_connect.php';
include 'notifications.php';

// التحقق من تسجيل الدخول
$is_admin = false;
$is_student = false;
$user_id = 0;
$user_type = '';

if (isset($_SESSION['admin'])) {
    $is_admin = true;
    $user_id = $_SESSION['admin']['id'];
    $user_type = 'admin';
} elseif (isset($_SESSION['student'])) {
    $is_student = true;
    $user_id = $_SESSION['student']['id'];
    $user_type = 'student';
} else {
    // توجيه المستخدم غير المسجل دخوله
    header("Location: index.html");
    exit();
}

// معالجة عمليات الإشعارات
$message = '';
$message_type = '';

// تحديث حالة قراءة إشعار واحد
if (isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    $notification_id = intval($_GET['mark_read']);
    
    if (mark_notification_as_read($notification_id)) {
        $message = 'تم تحديث حالة قراءة الإشعار بنجاح';
        $message_type = 'success';
    } else {
        $message = 'حدث خطأ أثناء تحديث حالة قراءة الإشعار';
        $message_type = 'danger';
    }
}

// تحديث حالة قراءة جميع الإشعارات
if (isset($_GET['mark_all_read'])) {
    if (mark_all_notifications_as_read($user_id, $user_type)) {
        $message = 'تم تحديث حالة قراءة جميع الإشعارات بنجاح';
        $message_type = 'success';
    } else {
        $message = 'حدث خطأ أثناء تحديث حالة قراءة الإشعارات';
        $message_type = 'danger';
    }
}

// حذف إشعار
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $notification_id = intval($_GET['delete']);
    
    if (delete_notification($notification_id)) {
        $message = 'تم حذف الإشعار بنجاح';
        $message_type = 'success';
    } else {
        $message = 'حدث خطأ أثناء حذف الإشعار';
        $message_type = 'danger';
    }
}

// الإعدادات الافتراضية للتصفح
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$offset = ($page - 1) * $limit;

// فلترة الإشعارات
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$unread_only = ($filter == 'unread');

// جلب الإشعارات
$notifications = get_user_notifications($user_id, $user_type, $limit, $offset, $unread_only);

// جلب العدد الإجمالي للإشعارات
$total_notifications = 0;
$stmt = $conn->prepare("
    SELECT COUNT(*) as total FROM notifications 
    WHERE user_id = ? AND user_type = ? AND (is_read = 0 OR ? = 'all')
");
$filter_param = $filter;
$stmt->bind_param("iss", $user_id, $user_type, $filter_param);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();
$total_notifications = $row['total'];

// حساب عدد الصفحات
$total_pages = ceil($total_notifications / $limit);

// تنسيق التاريخ
function format_date($date) {
    $date_obj = new DateTime($date);
    $now = new DateTime();
    $diff = $date_obj->diff($now);
    
    if ($diff->days == 0) {
        if ($diff->h == 0) {
            if ($diff->i == 0) {
                return "منذ ثوان";
            } else {
                return "منذ {$diff->i} دقيقة";
            }
        } else {
            return "منذ {$diff->h} ساعة";
        }
    } elseif ($diff->days == 1) {
        return "الأمس";
    } elseif ($diff->days < 7) {
        return "منذ {$diff->days} أيام";
    } else {
        return $date_obj->format('d-m-Y');
    }
}

// الصفحة المعادة
$return_page = $is_admin ? 'dashboard.php' : 'view_requests.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات | <?php echo SITE_NAME; ?></title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- أنماط CSS الأساسية -->
    <link rel="stylesheet" href="styles.css">
    <!-- أنماط الوضع المظلم -->
    <link rel="stylesheet" href="styles_dark.css">
    <style>
        .notification-card {
            margin-bottom: 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .notification-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .notification-unread {
            border-right: 4px solid var(--bs-primary);
            background-color: rgba(13, 110, 253, 0.05);
        }
        .notification-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .notification-type-info {
            border-right-color: #0dcaf0;
        }
        .notification-type-success {
            border-right-color: #198754;
        }
        .notification-type-warning {
            border-right-color: #ffc107;
        }
        .notification-type-danger {
            border-right-color: #dc3545;
        }
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 15px;
        }
        .notification-icon-info {
            background-color: #0dcaf0;
        }
        .notification-icon-success {
            background-color: #198754;
        }
        .notification-icon-warning {
            background-color: #ffc107;
        }
        .notification-icon-danger {
            background-color: #dc3545;
        }
        .notification-actions {
            white-space: nowrap;
        }
        .pagination {
            margin-top: 20px;
        }
        .empty-notifications {
            padding: 30px;
            text-align: center;
        }
        .empty-notifications i {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-bell me-2"></i>
                الإشعارات
            </h1>
            <div>
                <a href="<?php echo $return_page; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة
                </a>
            </div>
        </div>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <?php if ($message_type == 'danger'): ?>
                    <i class="fas fa-exclamation-triangle me-2"></i>
                <?php else: ?>
                    <i class="fas fa-check-circle me-2"></i>
                <?php endif; ?>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="btn-group" role="group">
                        <a href="view_notifications.php?filter=all" class="btn btn-outline-primary <?php echo $filter == 'all' ? 'active' : ''; ?>">
                            <i class="fas fa-list me-1"></i>
                            جميع الإشعارات
                        </a>
                        <a href="view_notifications.php?filter=unread" class="btn btn-outline-primary <?php echo $filter == 'unread' ? 'active' : ''; ?>">
                            <i class="fas fa-envelope me-1"></i>
                            غير المقروءة
                        </a>
                    </div>
                    <div>
                        <a href="view_notifications.php?mark_all_read=1" class="btn btn-success">
                            <i class="fas fa-check-double me-1"></i>
                            تحديد الكل كمقروء
                        </a>
                    </div>
                </div>
                
                <?php if (empty($notifications)): ?>
                    <div class="empty-notifications">
                        <i class="far fa-bell-slash"></i>
                        <h4>لا توجد إشعارات</h4>
                        <p class="text-muted">ستظهر الإشعارات الجديدة هنا عند تلقيها</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($notifications as $notification): ?>
                        <div class="card notification-card <?php echo $notification['is_read'] ? '' : 'notification-unread'; ?> notification-type-<?php echo $notification['type']; ?>">
                            <div class="card-body">
                                <div class="d-flex">
                                    <div class="notification-icon notification-icon-<?php echo $notification['type']; ?>">
                                        <?php 
                                        $icon = 'info-circle';
                                        switch ($notification['type']) {
                                            case 'success': $icon = 'check-circle'; break;
                                            case 'warning': $icon = 'exclamation-triangle'; break;
                                            case 'danger': $icon = 'times-circle'; break;
                                        }
                                        ?>
                                        <i class="fas fa-<?php echo $icon; ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h5 class="card-title mb-1"><?php echo htmlspecialchars($notification['title']); ?></h5>
                                        <p class="notification-time mb-2">
                                            <i class="far fa-clock me-1"></i>
                                            <?php echo format_date($notification['created_at']); ?>
                                        </p>
                                        <p class="card-text mb-0"><?php echo htmlspecialchars($notification['message']); ?></p>
                                    </div>
                                    <div class="notification-actions">
                                        <?php if (!$notification['is_read']): ?>
                                            <a href="view_notifications.php?mark_read=<?php echo $notification['id']; ?>&filter=<?php echo $filter; ?>&page=<?php echo $page; ?>" class="btn btn-sm btn-outline-primary" title="تحديد كمقروء">
                                                <i class="fas fa-envelope-open"></i>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if ($notification['link']): ?>
                                            <a href="<?php echo $notification['link']; ?>" class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <a href="view_notifications.php?delete=<?php echo $notification['id']; ?>&filter=<?php echo $filter; ?>&page=<?php echo $page; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟');">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="صفحات الإشعارات">
                            <ul class="pagination justify-content-center">
                                <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="view_notifications.php?filter=<?php echo $filter; ?>&page=<?php echo $page - 1; ?>" aria-label="السابق">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="view_notifications.php?filter=<?php echo $filter; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="view_notifications.php?filter=<?php echo $filter; ?>&page=<?php echo $page + 1; ?>" aria-label="التالي">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- ملف JavaScript الرئيسي للتطبيق -->
    <script src="app.js"></script>
</body>
</html> 