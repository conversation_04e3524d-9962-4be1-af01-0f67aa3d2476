<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

include 'db_connect.php';

$message = ""; // متغير لتخزين الرسائل

// حذف طلب
if (isset($_GET['delete'])) {
    $request_id = $_GET['delete'];
    $sql = "DELETE FROM requests WHERE id = $request_id";
    if ($conn->query($sql) === TRUE) {
        $message = "تم حذف الطلب بنجاح";
    } else {
        $message = "خطأ في حذف الطلب: " . $conn->error;
    }
}

// تغيير حالة طلب
if (isset($_GET['change_status']) && isset($_GET['request_id'])) {
    $request_id = $_GET['request_id'];
    $new_status = $_GET['change_status'];
    
    if (in_array($new_status, ['pending', 'accepted', 'rejected'])) {
        $sql = "UPDATE requests SET status = '$new_status' WHERE id = $request_id";
        if ($conn->query($sql) === TRUE) {
            $message = "تم تغيير حالة الطلب بنجاح";
        } else {
            $message = "خطأ في تغيير حالة الطلب: " . $conn->error;
        }
    }
}

// استرجاع جميع الطلبات مع معلومات الطلاب
$result = $conn->query("SELECT r.id, s.baccalaureate_number, s.name, s.residence, r.residence_requested, r.status, r.release_status, r.created_at 
                         FROM requests r 
                         JOIN students s ON r.student_id = s.id 
                         ORDER BY r.created_at DESC");

// دالة لترجمة حالة الطلب
function translateStatus($status) {
    switch($status) {
        case 'pending':
            return 'قيد الدراسة';
        case 'accepted':
            return 'مقبول';
        case 'rejected':
            return 'مرفوض';
        default:
            return $status;
    }
}

// دالة لترجمة حالة التبرئة
function translateReleaseStatus($status) {
    return ($status == 'yes') ? 'تمت التبرئة' : 'لم تتم التبرئة';
}

// دالة لإرجاع لون الشارة حسب الحالة
function getStatusBadgeClass($status) {
    switch($status) {
        case 'pending':
            return 'bg-warning';
        case 'accepted':
            return 'bg-success';
        case 'rejected':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

// دالة لإرجاع لون الشارة حسب حالة التبرئة
function getReleaseStatusBadgeClass($status) {
    return ($status == 'yes') ? 'bg-success' : 'bg-danger';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #007bff;
        }
        .main-content {
            padding: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .status-filter .btn {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="text-center p-3 mb-3 border-bottom">
                    <h5 class="mb-0">منصة التحويلات</h5>
                    <p class="small mb-0">لوحة تحكم المدير</p>
                </div>
                <ul class="nav flex-column p-2">
                    <li class="nav-item">
                        <a class="nav-link rounded" href="admin_interface.html">
                            <i class="fas fa-home me-2"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active rounded" href="manage_requests.php">
                            <i class="fas fa-exchange-alt me-2"></i>
                            إدارة الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_students.php">
                            <i class="fas fa-user-graduate me-2"></i>
                            إدارة الطلبة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_residences.php">
                            <i class="fas fa-building me-2"></i>
                            إدارة الإقامات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="create_account.php">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب مكلف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_accounts.php">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة الحسابات
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link rounded bg-danger text-white" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة الطلبات</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-file-export me-1"></i> تصدير
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-print me-1"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert <?php echo strpos($message, 'تم') !== false ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">قائمة الطلبات</h5>
                            <div class="status-filter">
                                <div class="btn-group" role="group" aria-label="تصفية حسب الحالة">
                                    <a href="?status=all" class="btn btn-sm <?php echo !isset($_GET['status']) || $_GET['status'] == 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                        الكل
                                    </a>
                                    <a href="?status=pending" class="btn btn-sm <?php echo isset($_GET['status']) && $_GET['status'] == 'pending' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                        قيد الدراسة
                                    </a>
                                    <a href="?status=accepted" class="btn btn-sm <?php echo isset($_GET['status']) && $_GET['status'] == 'accepted' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                        مقبول
                                    </a>
                                    <a href="?status=rejected" class="btn btn-sm <?php echo isset($_GET['status']) && $_GET['status'] == 'rejected' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                        مرفوض
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>رقم البكالوريا</th>
                                        <th>اسم الطالب</th>
                                        <th>الإقامة الحالية</th>
                                        <th>الإقامة المطلوبة</th>
                                        <th>حالة الطلب</th>
                                        <th>حالة التبرئة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if ($result->num_rows > 0):
                                        while ($row = $result->fetch_assoc()): 
                                            // تصفية حسب الحالة إذا تم تحديدها
                                            if (isset($_GET['status']) && $_GET['status'] != 'all' && $row['status'] != $_GET['status']) {
                                                continue;
                                            }
                                    ?>
                                    <tr>
                                        <td><?php echo $row['id']; ?></td>
                                        <td><?php echo $row['baccalaureate_number']; ?></td>
                                        <td><?php echo $row['name']; ?></td>
                                        <td><?php echo $row['residence']; ?></td>
                                        <td><?php echo $row['residence_requested']; ?></td>
                                        <td>
                                            <span class="badge <?php echo getStatusBadgeClass($row['status']); ?>">
                                                <?php echo translateStatus($row['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo getReleaseStatusBadgeClass($row['release_status']); ?>">
                                                <?php echo translateReleaseStatus($row['release_status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <?php if ($row['status'] == 'pending'): ?>
                                                    <a href="?change_status=accepted&request_id=<?php echo $row['id']; ?>" class="btn btn-success">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    <a href="?change_status=rejected&request_id=<?php echo $row['id']; ?>" class="btn btn-danger">
                                                        <i class="fas fa-times"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="?change_status=pending&request_id=<?php echo $row['id']; ?>" class="btn btn-warning">
                                                        <i class="fas fa-redo"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="?delete=<?php echo $row['id']; ?>" class="btn btn-secondary" onclick="return confirm('هل أنت متأكد من حذف هذا الطلب؟')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php 
                                        endwhile; 
                                    else: 
                                    ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4">لا توجد طلبات</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
$conn->close();
?>