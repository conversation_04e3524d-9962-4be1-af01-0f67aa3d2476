-- إنشاء جدول الإشعارات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    user_type ENUM('admin', 'student') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type <PERSON>NU<PERSON>('info', 'success', 'warning', 'danger') DEFAULT 'info',
    link VARCHAR(255) DEFAULT NULL,
    is_read BOOLEAN DEFAULT 0,
    read_at DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Display confirmation message
SELECT 'Notifications table has been created successfully' AS Result; 