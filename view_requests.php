<?php
session_start(); // بدء الجلسة

// التحقق من تسجيل دخول الطالب
if (!isset($_SESSION['student'])) {
    header("Location: student_login.php"); // توجيه إلى صفحة تسجيل الدخول إذا لم يكن مسجلاً الدخول
    exit();
}

// الاتصال بقاعدة البيانات
include 'db_connect.php';

// استرجاع معلومات الطالب
$student_id = $_SESSION['student']['id']; // الحصول على ID الطالب

// استرجاع معلومات الطالب المفصلة
$student_query = "SELECT * FROM students WHERE id = $student_id";
$student_result = $conn->query($student_query);
$student_data = $student_result->fetch_assoc();

// استرجاع الطلبات الخاصة بالطالب
$query = "SELECT * FROM requests WHERE student_id = $student_id ORDER BY created_at DESC";
$result = $conn->query($query);

// دالة لترجمة حالة الطلب
function translateStatus($status) {
    switch($status) {
        case 'pending':
            return 'قيد الدراسة';
        case 'accepted':
            return 'مقبول';
        case 'rejected':
            return 'مرفوض';
        default:
            return $status;
    }
}

// دالة لتحديد لون حالة الطلب
function getStatusColor($status) {
    switch($status) {
        case 'pending':
            return 'warning';
        case 'accepted':
            return 'success';
        case 'rejected':
            return 'danger';
        default:
            return 'secondary';
    }
}

// دالة لترجمة حالة التبرئة
function translateReleaseStatus($status) {
    return ($status == 'yes') ? 'تمت التبرئة' : 'لم تتم التبرئة';
}

// دالة لتحديد لون حالة التبرئة
function getReleaseStatusColor($status) {
    return ($status == 'yes') ? 'success' : 'secondary';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض طلباتي | منصة التحويلات بين الإقامات الجامعية</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .main-content {
            flex: 1;
            padding: 2rem 0;
        }
        .student-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .student-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-right: 4px solid #0d6efd;
        }
        .student-info h5 {
            color: #0d6efd;
            margin-bottom: 1rem;
        }
        .student-info .info-item {
            margin-bottom: 0.5rem;
        }
        .student-info .info-label {
            font-weight: bold;
            color: #495057;
        }
        .requests-table {
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .navbar-nav .nav-link.active {
            font-weight: bold;
            color: #0d6efd;
        }
        .student-badge {
            background-color: #e9ecef;
            color: #495057;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        .empty-requests {
            text-align: center;
            padding: 3rem 0;
            background-color: #f8f9fa;
            border-radius: 10px;
            border: 1px dashed #dee2e6;
        }
        .empty-requests i {
            font-size: 3rem;
            color: #adb5bd;
            margin-bottom: 1rem;
        }
        .empty-requests p {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- القائمة العلوية -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-exchange-alt text-primary me-2"></i>
                منصة التحويلات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="submit_request.php">
                            <i class="fas fa-plus-circle me-1"></i>
                            تقديم طلب جديد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="view_requests.php">
                            <i class="fas fa-list-alt me-1"></i>
                            عرض طلباتي
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="student-badge me-3">
                        <i class="fas fa-user-graduate"></i>
                        <?php echo $student_data['first_name'] . ' ' . $student_data['last_name']; ?>
                    </span>
                    <a href="logout.php" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">
                        <i class="fas fa-user-circle text-primary me-2"></i>
                        معلومات الطالب وطلباته
                    </h2>
                </div>

                <!-- معلومات الطالب -->
                <div class="student-info">
                    <h5>
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الطالب
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">رقم البكالوريا:</span>
                                <?php echo $student_data['baccalaureate_number']; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">الاسم الكامل:</span>
                                <?php echo $student_data['first_name'] . ' ' . $student_data['last_name']; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">تاريخ الميلاد:</span>
                                <?php 
                                //date_default_timezone_set('Africa/Casablanca');
                                echo date('Y-m-d', strtotime($student_data['birth_date'])); 
                                ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">مكان الميلاد:</span>
                                <?php echo $student_data['birth_place']; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">الجنس:</span>
                                <span class="badge bg-<?php echo $student_data['gender'] == 'ذكر' ? 'info' : 'danger'; ?> text-white">
                                    <i class="fas fa-<?php echo $student_data['gender'] == 'ذكر' ? 'male' : 'female'; ?> me-1"></i>
                                    <?php echo $student_data['gender']; ?>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">الإقامة الحالية:</span>
                                <span class="badge bg-primary"><?php echo $student_data['residence']; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">التخصص:</span>
                                <?php echo $student_data['specialization']; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">السنة الدراسية:</span>
                                <?php echo $student_data['study_year']; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الطلبات -->
                <div class="card requests-table">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list-alt me-2"></i>
                            طلباتي
                        </h5>
                        <span class="badge bg-primary">
                            <?php echo $result->num_rows; ?> طلب
                        </span>
                    </div>
                    <div class="card-body">
                        <?php if ($result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th scope="col">رقم الطلب</th>
                                            <th scope="col">الإقامة المطلوبة</th>
                                            <th scope="col">حالة الطلب</th>
                                            <th scope="col">حالة التبرئة</th>
                                            <th scope="col">تاريخ الطلب</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($row = $result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    #<?php echo $row['id']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info text-dark">
                                                    <i class="fas fa-building me-1"></i>
                                                    <?php echo $row['residence_requested']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getStatusColor($row['status']); ?>">
                                                    <i class="fas fa-<?php echo $row['status'] == 'pending' ? 'clock' : ($row['status'] == 'accepted' ? 'check-circle' : 'times-circle'); ?> me-1"></i>
                                                    <?php echo translateStatus($row['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getReleaseStatusColor($row['release_status']); ?>">
                                                    <i class="fas fa-<?php echo $row['release_status'] == 'yes' ? 'check-circle' : 'hourglass-half'; ?> me-1"></i>
                                                    <?php echo translateReleaseStatus($row['release_status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <i class="fas fa-calendar-alt me-1 text-muted"></i>
                                                <?php echo date('Y-m-d', strtotime($row['created_at'])); ?>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-requests">
                                <i class="fas fa-folder-open d-block"></i>
                                <p>لا توجد طلبات مقدمة حتى الآن.</p>
                                <a href="submit_request.php" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-1"></i>
                                    تقديم طلب جديد
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الفوتر -->
    <footer class="bg-dark text-white text-center py-3 mt-auto">
        <div class="container">
            <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
        </div>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
$conn->close();
?>
