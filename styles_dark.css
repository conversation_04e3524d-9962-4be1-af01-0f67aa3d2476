/* 
 * نمط الوضع المظلم لمنصة التحويلات بين الإقامات الجامعية
 * يستخدم هذا الملف مع ملف styles.css الأساسي
 */

:root {
    --dark-bg: #121212;
    --dark-card-bg: #1e1e1e;
    --dark-text: #e0e0e0;
    --dark-text-muted: #a0a0a0;
    --dark-border: #333333;
    --dark-hover: #2a2a2a;
    --dark-primary: #0d6efd;
    --dark-primary-hover: #0b5ed7;
    --dark-input-bg: #2c2c2c;
}

/* ======= الوضع المظلم الأساسي ======= */
body.dark-mode {
    background-color: var(--dark-bg) !important;
    color: var(--dark-text) !important;
}

.dark-mode .card,
.dark-mode .modal-content,
.dark-mode .login-card,
.dark-mode .reset-card,
.dark-mode .forgot-card,
.dark-mode .stats-card {
    background-color: var(--dark-card-bg) !important;
    border-color: var(--dark-border) !important;
}

.dark-mode .table {
    color: var(--dark-text) !important;
}

.dark-mode .text-muted {
    color: var(--dark-text-muted) !important;
}

.dark-mode hr {
    border-color: var(--dark-border) !important;
}

/* ======= المدخلات والنماذج ======= */
.dark-mode .form-control,
.dark-mode .form-select,
.dark-mode .input-group-text {
    background-color: var(--dark-input-bg) !important;
    border-color: var(--dark-border) !important;
    color: var(--dark-text) !important;
}

.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
    background-color: var(--dark-input-bg) !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

.dark-mode .form-floating label {
    color: var(--dark-text-muted) !important;
}

/* ======= الجداول ======= */
.dark-mode .table {
    border-color: var(--dark-border) !important;
}

.dark-mode .table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

.dark-mode .table-hover > tbody > tr:hover {
    background-color: rgba(255, 255, 255, 0.08) !important;
}

.dark-mode .table th,
.dark-mode .table td {
    border-color: var(--dark-border) !important;
}

/* ======= الأزرار والروابط ======= */
.dark-mode .btn-outline-primary {
    color: var(--dark-primary) !important;
    border-color: var(--dark-primary) !important;
}

.dark-mode .btn-outline-primary:hover {
    background-color: var(--dark-primary) !important;
    color: white !important;
}

.dark-mode .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

.dark-mode a {
    color: var(--dark-primary) !important;
}

.dark-mode a:hover {
    color: var(--dark-primary-hover) !important;
}

/* ======= لوحة التنقل الجانبية ======= */
.dark-mode .sidebar {
    background-color: #1a1a1a !important;
}

.dark-mode .sidebar .nav-link {
    color: rgba(255, 255, 255, 0.75) !important;
}

.dark-mode .sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

.dark-mode .sidebar .nav-link.active {
    background-color: var(--dark-primary) !important;
}

.dark-mode .sidebar-heading {
    color: var(--dark-text-muted) !important;
}

/* ======= التنبيهات والرسائل ======= */
.dark-mode .alert-success {
    background-color: rgba(25, 135, 84, 0.2) !important;
    color: #75b798 !important;
    border-color: rgba(25, 135, 84, 0.4) !important;
}

.dark-mode .alert-danger {
    background-color: rgba(220, 53, 69, 0.2) !important;
    color: #ea868f !important;
    border-color: rgba(220, 53, 69, 0.4) !important;
}

.dark-mode .alert-warning {
    background-color: rgba(255, 193, 7, 0.2) !important;
    color: #ffda6a !important;
    border-color: rgba(255, 193, 7, 0.4) !important;
}

.dark-mode .alert-info {
    background-color: rgba(13, 202, 240, 0.2) !important;
    color: #6edff6 !important;
    border-color: rgba(13, 202, 240, 0.4) !important;
}

/* ======= الرأس والتذييل ======= */
.dark-mode .main-header {
    background-color: var(--dark-primary) !important;
}

.dark-mode footer {
    background-color: #1a1a1a !important;
}

/* ======= قياس قوة كلمة المرور ======= */
.dark-mode .strength-meter {
    background-color: #333 !important;
}

/* ======= زر تبديل الوضع المظلم ======= */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #343a40;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
    background-color: #0d6efd;
}

.dark-mode .dark-mode-toggle {
    background-color: #0d6efd;
}

.dark-mode .dark-mode-toggle:hover {
    background-color: #0b5ed7;
}

/* ======= المخططات البيانية ======= */
.dark-mode .chart-container canvas {
    filter: brightness(0.9) contrast(1.1);
} 