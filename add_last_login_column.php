<?php
// This script adds the 'last_login' column to the residence_managers table if it doesn't exist

// Start session for security
session_start();

// Security check - Allow only in development environment or for administrators
$allowed = false;

// Check if user is admin
if (isset($_SESSION['admin']) && $_SESSION['admin']['role'] == 'manager') {
    $allowed = true;
}

// Check if it's a localhost request (development environment)
if (in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1'])) {
    $allowed = true;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث بنية قاعدة البيانات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
</head>
<body>
    <div class="container py-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">تحديث بنية قاعدة البيانات</h3>
            </div>
            <div class="card-body">
            <?php
            if ($allowed) {
                // Include database connection
                require_once 'db_connect.php';

                echo '<h4>جاري إضافة عمود last_login...</h4>';
                
                // SQL query to add the column if it doesn't exist
                $sql = "ALTER TABLE residence_managers ADD COLUMN IF NOT EXISTS last_login DATETIME NULL COMMENT 'Last login timestamp'";
                
                // Execute the query
                if ($conn->query($sql) === TRUE) {
                    echo '<div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم إضافة عمود last_login بنجاح إلى جدول residence_managers
                    </div>';
                    echo '<p>يمكنك الآن <a href="admin_login.php" class="btn btn-primary btn-sm">تسجيل الدخول</a> بدون مشاكل.</p>';
                } else {
                    echo '<div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في إضافة العمود: ' . $conn->error . '
                    </div>';
                }
                
                // Close the connection
                $conn->close();
            } else {
                echo '<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    غير مسموح بالوصول. يجب أن تكون مسؤولاً للقيام بهذه العملية.
                </div>';
            }
            ?>
            </div>
            <div class="card-footer">
                <a href="index.html" class="btn btn-secondary">العودة إلى الصفحة الرئيسية</a>
            </div>
        </div>
    </div>
</body>
</html> 