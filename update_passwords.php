<?php
include 'db_connect.php';

// استرجاع جميع المستخدمين
$sql = "SELECT id, username, password FROM residence_managers";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<h2>تحديث كلمات المرور...</h2>";
    
    while($row = $result->fetch_assoc()) {
        $id = $row['id'];
        $plain_password = $row['password'];
        
        // تشفير كلمة المرور باستخدام password_hash
        $hashed_password = password_hash($plain_password, PASSWORD_DEFAULT);
        
        // تحديث كلمة المرور في قاعدة البيانات
        $update_sql = "UPDATE residence_managers SET password = ? WHERE id = ?";
        $stmt = $conn->prepare($update_sql);
        $stmt->bind_param("si", $hashed_password, $id);
        
        if ($stmt->execute()) {
            echo "تم تحديث كلمة المرور للمستخدم: " . $row['username'] . "<br>";
        } else {
            echo "خطأ في تحديث كلمة المرور للمستخدم: " . $row['username'] . "<br>";
        }
    }
    
    echo "<h3>تم الانتهاء من تحديث كلمات المرور!</h3>";
    echo "<p>يمكنك الآن استخدام نظام تسجيل الدخول الجديد.</p>";
} else {
    echo "لم يتم العثور على أي مستخدمين.";
}

$conn->close();
?>