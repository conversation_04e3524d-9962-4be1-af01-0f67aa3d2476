<?php
session_start();
require_once 'config.php';
require_once 'db_connect.php';
require_once 'db_helper.php';

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    redirect('admin_login.php');
}

// للتأكد من أن ميزة تصدير البيانات مفعلة
if (!FEATURE_EXPORT_DATA) {
    redirect('dashboard.php', ['error' => 'feature_disabled']);
}

$message = "";
$message_type = "";

// استعلامات للحصول على البيانات المتاحة للتصدير
$tables_info = [
    'students' => [
        'name' => 'الطلبة',
        'description' => 'قائمة جميع الطلبة المسجلين في النظام مع بياناتهم الكاملة',
        'icon' => 'fa-user-graduate',
        'query' => "SELECT id, baccalaureate_number, last_name, first_name, birth_date, birth_place, residence, specialization, study_year, gender FROM students",
        'filename' => 'students_export'
    ],
    'requests' => [
        'name' => 'طلبات التحويل',
        'description' => 'جميع طلبات التحويل مع حالتها وتاريخها',
        'icon' => 'fa-exchange-alt',
        'query' => "SELECT r.id, s.baccalaureate_number, CONCAT(s.last_name, ' ', s.first_name) as student_name, 
                    s.residence as current_residence, r.residence_requested, r.status, r.release_status, r.created_at 
                    FROM requests r 
                    JOIN students s ON r.student_id = s.id",
        'filename' => 'requests_export'
    ],
    'residences' => [
        'name' => 'الإقامات الجامعية',
        'description' => 'قائمة الإقامات الجامعية مع سعتها وعدد الساكنين',
        'icon' => 'fa-building',
        'query' => "SELECT * FROM residences",
        'filename' => 'residences_export'
    ],
    'residence_managers' => [
        'name' => 'مسؤولي الإقامات',
        'description' => 'قائمة مسؤولي الإقامات (بدون كلمات المرور)',
        'icon' => 'fa-users-cog',
        'query' => "SELECT id, username, role, residence_name, last_login FROM residence_managers",
        'filename' => 'managers_export'
    ]
];

// إذا تم تقديم نموذج التصدير
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['export'])) {
    $export_table = $_POST['export_table'];
    $export_format = $_POST['export_format'];
    
    // التحقق من وجود الجدول المطلوب
    if (!isset($tables_info[$export_table])) {
        $message = "خطأ: الجدول المطلوب غير موجود";
        $message_type = "danger";
    } else {
        $table_info = $tables_info[$export_table];
        $query = $table_info['query'];
        $filename = $table_info['filename'] . '_' . date('Y-m-d');
        
        // تنفيذ الاستعلام
        $data = db_fetch_all($query);
        
        if (empty($data)) {
            $message = "لا توجد بيانات للتصدير في الجدول المحدد";
            $message_type = "warning";
        } else {
            // تسجيل عملية التصدير
            log_message("تم تصدير بيانات {$table_info['name']} بتنسيق $export_format", "info");
            
            // تحديد التنسيق وتصدير البيانات
            switch ($export_format) {
                case 'csv':
                    export_to_csv($data, $filename);
                    break;
                    
                case 'excel':
                    export_to_excel($data, $filename);
                    break;
                    
                case 'json':
                    export_to_json($data, $filename);
                    break;
                    
                case 'pdf':
                    export_to_pdf($data, $filename, $table_info['name']);
                    break;
                    
                default:
                    $message = "خطأ: تنسيق التصدير غير مدعوم";
                    $message_type = "danger";
            }
        }
    }
}

/**
 * تصدير البيانات بتنسيق CSV
 */
function export_to_csv($data, $filename) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // إضافة علامة ترتيب البايت (BOM) لدعم الأحرف العربية في Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة أسماء الأعمدة
    fputcsv($output, array_keys($data[0]));
    
    // كتابة البيانات
    foreach ($data as $row) {
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit;
}

/**
 * تصدير البيانات بتنسيق Excel
 */
function export_to_excel($data, $filename) {
    // تصدير بتنسيق CSV مع تغيير الامتداد إلى XLS
    // يمكن استخدام مكتبة PHPExcel لعمل تصدير أكثر تقدماً
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    
    echo '<table border="1">';
    
    // كتابة رأس الجدول
    echo '<tr>';
    foreach (array_keys($data[0]) as $key) {
        echo '<th>' . htmlspecialchars($key) . '</th>';
    }
    echo '</tr>';
    
    // كتابة بيانات الجدول
    foreach ($data as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            echo '<td>' . htmlspecialchars($cell) . '</td>';
        }
        echo '</tr>';
    }
    
    echo '</table>';
    exit;
}

/**
 * تصدير البيانات بتنسيق JSON
 */
function export_to_json($data, $filename) {
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.json"');
    
    echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * تصدير البيانات بتنسيق PDF
 */
function export_to_pdf($data, $filename, $title) {
    // ملاحظة: هذه وظيفة بسيطة. للإنتاج الحقيقي، يُفضل استخدام مكتبة مثل TCPDF أو FPDF
    
    // تحويل البيانات إلى جدول HTML
    $html = '<h1>' . htmlspecialchars($title) . '</h1>';
    $html .= '<table border="1" cellspacing="0" cellpadding="5" width="100%">';
    
    // إضافة رأس الجدول
    $html .= '<tr style="background-color: #f2f2f2;">';
    foreach (array_keys($data[0]) as $key) {
        $html .= '<th>' . htmlspecialchars($key) . '</th>';
    }
    $html .= '</tr>';
    
    // إضافة بيانات الجدول
    foreach ($data as $row) {
        $html .= '<tr>';
        foreach ($row as $cell) {
            $html .= '<td>' . htmlspecialchars($cell) . '</td>';
        }
        $html .= '</tr>';
    }
    
    $html .= '</table>';
    
    // طباعة البيانات بتنسيق يمكن طباعته
    header('Content-Type: text/html; charset=utf-8');
    
    echo '<!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>' . htmlspecialchars($title) . '</title>
        <style>
            body { font-family: Arial, sans-serif; }
            @media print {
                body { margin: 0; padding: 20px; }
                .no-print { display: none; }
            }
            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f2f2f2; }
            h1 { text-align: center; }
            .btn {
                padding: 8px 16px;
                margin: 5px;
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
            }
            .btn-print {
                background-color: #28a745;
            }
            .btn-back {
                background-color: #dc3545;
            }
            .btn:hover {
                opacity: 0.9;
            }
        </style>
    </head>
    <body>
        <div class="no-print" style="text-align: center; margin-bottom: 20px;">
            <button class="btn btn-print" onclick="window.print()"><i class="fas fa-print"></i> طباعة</button>
            <a class="btn btn-back" href="export_data.php"><i class="fas fa-arrow-right"></i> العودة للصفحة السابقة</a>
        </div>
        
        ' . $html . '
        
        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button class="btn btn-print" onclick="window.print()"><i class="fas fa-print"></i> طباعة</button>
            <a class="btn btn-back" href="export_data.php"><i class="fas fa-arrow-right"></i> العودة للصفحة السابقة</a>
        </div>
        
        <!-- إضافة Font Awesome للأيقونات -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    </body>
    </html>';
    exit;
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصدير البيانات | <?php echo SITE_NAME; ?></title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .sidebar {
            background-color: #343a40;
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.2rem 0;
            border-radius: 5px;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .sidebar .nav-link.active {
            background-color: var(--bs-primary);
            color: white;
        }
        .sidebar-heading {
            font-size: 0.85rem;
            text-transform: uppercase;
            padding: 1rem;
            opacity: 0.6;
        }
        .main-content {
            padding: 20px;
        }
        .export-card {
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            height: 100%;
        }
        .export-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .export-card.selected {
            border-color: var(--bs-primary);
            border-width: 2px;
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }
        .export-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--bs-primary);
        }
        .export-format {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            background-color: #f8f9fa;
        }
        .format-option {
            cursor: pointer;
            padding: 15px;
            border-radius: 5px;
            transition: all 0.2s;
            text-align: center;
        }
        .format-option:hover {
            background-color: #e9ecef;
        }
        .format-option.selected {
            background-color: #e3f2fd;
            border: 1px solid #90caf9;
        }
        .format-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4 p-3">
                        <i class="fas fa-university fa-3x mb-3 text-primary"></i>
                        <h5 class="fw-bold"><?php echo SITE_NAME; ?></h5>
                        <div class="small opacity-75">لوحة تحكم المدير</div>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_requests.php">
                                <i class="fas fa-exchange-alt me-2"></i>
                                إدارة الطلبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_students.php">
                                <i class="fas fa-user-graduate me-2"></i>
                                إدارة الطلبة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_residences.php">
                                <i class="fas fa-building me-2"></i>
                                إدارة الإقامات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_accounts.php">
                                <i class="fas fa-users-cog me-2"></i>
                                إدارة الحسابات
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-3">
                    
                    <div class="sidebar-heading">
                        أدوات
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="import_students.php">
                                <i class="fas fa-file-import me-2"></i>
                                استيراد بيانات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="export_data.php">
                                <i class="fas fa-file-export me-2"></i>
                                تصدير بيانات
                            </a>
                        </li>
                        <?php if (FEATURE_AUTOMATIC_BACKUP): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="backup.php">
                                <i class="fas fa-database me-2"></i>
                                النسخ الاحتياطي
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                    
                    <hr class="my-3">
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-file-export text-primary me-2"></i>
                        تصدير البيانات
                    </h1>
                </div>
                
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title mb-4">اختر البيانات التي تريد تصديرها</h5>
                                
                                <form method="post" id="exportForm">
                                    <div class="row mb-4">
                                        <?php foreach ($tables_info as $table_key => $table): ?>
                                            <div class="col-md-3 mb-3">
                                                <div class="card export-card" data-table="<?php echo $table_key; ?>" onclick="selectTable(this)">
                                                    <div class="card-body text-center">
                                                        <i class="fas <?php echo $table['icon']; ?> export-icon"></i>
                                                        <h5 class="card-title"><?php echo $table['name']; ?></h5>
                                                        <p class="card-text small text-muted"><?php echo $table['description']; ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    
                                    <input type="hidden" name="export_table" id="exportTable" value="">
                                    <input type="hidden" name="export_format" id="exportFormat" value="">
                                    
                                    <div class="export-format mb-4" id="formatOptions" style="display: none;">
                                        <h5 class="mb-3">اختر تنسيق التصدير</h5>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="format-option" data-format="csv" onclick="selectFormat(this)">
                                                    <i class="fas fa-file-csv format-icon text-success"></i>
                                                    <h6>CSV</h6>
                                                    <small class="text-muted">تنسيق مناسب لمعالجات جداول البيانات</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="format-option" data-format="excel" onclick="selectFormat(this)">
                                                    <i class="fas fa-file-excel format-icon text-success"></i>
                                                    <h6>Excel</h6>
                                                    <small class="text-muted">تنسيق مناسب لبرنامج Microsoft Excel</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="format-option" data-format="pdf" onclick="selectFormat(this)">
                                                    <i class="fas fa-file-pdf format-icon text-danger"></i>
                                                    <h6>PDF</h6>
                                                    <small class="text-muted">تنسيق مناسب للطباعة والمشاركة</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="format-option" data-format="json" onclick="selectFormat(this)">
                                                    <i class="fas fa-file-code format-icon text-primary"></i>
                                                    <h6>JSON</h6>
                                                    <small class="text-muted">تنسيق مناسب للتطبيقات البرمجية</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center" id="exportButtonContainer" style="display: none;">
                                        <button type="submit" name="export" class="btn btn-primary btn-lg">
                                            <i class="fas fa-download me-2"></i>
                                            تصدير البيانات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- تاريخ التصدير -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-history me-2 text-primary"></i>
                                    تاريخ التصدير
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted text-center">سيتم إضافة تاريخ التصدير في الإصدارات القادمة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديد الجدول المراد تصديره
        function selectTable(element) {
            // إزالة التحديد السابق
            document.querySelectorAll('.export-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // تحديد البطاقة المختارة
            element.classList.add('selected');
            
            // تحديث قيمة الجدول المحدد
            document.getElementById('exportTable').value = element.dataset.table;
            
            // إظهار خيارات التنسيق
            document.getElementById('formatOptions').style.display = 'block';
            
            // إخفاء زر التصدير حتى يتم اختيار التنسيق
            document.getElementById('exportButtonContainer').style.display = 'none';
        }
        
        // تحديد تنسيق التصدير
        function selectFormat(element) {
            // إزالة التحديد السابق
            document.querySelectorAll('.format-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // تحديد التنسيق المختار
            element.classList.add('selected');
            
            // تحديث قيمة التنسيق المحدد
            document.getElementById('exportFormat').value = element.dataset.format;
            
            // إظهار زر التصدير
            document.getElementById('exportButtonContainer').style.display = 'block';
        }
    </script>
</body>
</html> 