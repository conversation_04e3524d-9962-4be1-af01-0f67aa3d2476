<?php
/**
 * ملف الإعدادات المركزي للموقع
 * يحتوي على مختلف الإعدادات والثوابت المستخدمة في التطبيق
 */

// معلومات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'base_transfer');

// إعدادات عامة للموقع
define('SITE_NAME', 'منصة التحويلات بين الإقامات الجامعية');
define('SITE_VERSION', '1.1.0');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات تسجيل الدخول والأمان
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_TIME', 300); // 5 دقائق بالثواني
define('SESSION_LIFETIME', 7200); // ساعتين بالثواني
define('PASSWORD_MIN_LENGTH', 8);

// إعدادات التحويلات والطلبات
define('MAX_REQUESTS_PER_STUDENT', 1);
define('REQUEST_EXPIRE_DAYS', 30);

// مسارات الملفات والمجلدات
define('UPLOAD_DIR', __DIR__ . '/uploads/');
define('BACKUP_DIR', __DIR__ . '/backups/');

// تكوين الرسائل والإشعارات
define('ENABLE_EMAIL_NOTIFICATIONS', false);
define('ENABLE_ACTIVITY_LOGGING', true);

// ضبط المنطقة الزمنية
date_default_timezone_set('Africa/Algiers');

// وظائف التطبيق المختلفة
define('FEATURE_STUDENT_PROFILE', true);
define('FEATURE_ADMIN_DASHBOARD', true);
define('FEATURE_AUTOMATIC_BACKUP', false);
define('FEATURE_EXPORT_DATA', true);

// وظائف المساعدة

/**
 * إعادة توجيه إلى صفحة معينة
 * 
 * @param string $location المسار للتوجيه إليه
 * @param array $params المعاملات الإضافية (اختياري)
 */
function redirect($location, $params = []) {
    $query = "";
    if (!empty($params)) {
        $query = '?' . http_build_query($params);
    }
    header("Location: $location$query");
    exit();
}

/**
 * الحصول على معلومات النظام لأغراض التصحيح
 * 
 * @return array معلومات النظام
 */
function get_system_info() {
    return [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'],
        'mysql_version' => function_exists('mysqli_get_client_info') ? mysqli_get_client_info() : 'غير معروف',
        'max_upload_size' => ini_get('upload_max_filesize'),
        'max_post_size' => ini_get('post_max_size'),
        'memory_limit' => ini_get('memory_limit'),
        'timezone' => date_default_timezone_get(),
    ];
}

/**
 * تسجيل رسالة في ملف السجل
 * 
 * @param string $message الرسالة
 * @param string $level مستوى الرسالة (info, warning, error)
 */
function log_message($message, $level = 'info') {
    if (ENABLE_ACTIVITY_LOGGING) {
        $log_file = __DIR__ . '/logs/app_' . date('Y-m-d') . '.log';
        $log_dir = dirname($log_file);
        
        // إنشاء مجلد السجلات إذا لم يكن موجوداً
        if (!file_exists($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
        file_put_contents($log_file, $log_entry, FILE_APPEND);
    }
}
?> 