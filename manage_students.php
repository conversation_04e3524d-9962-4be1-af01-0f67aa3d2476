<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

include 'db_connect.php';

$message = ""; // متغير لتخزين الرسائل
$message_type = ""; // متغير لتخزين نوع الرسالة

// استعلام لجلب قائمة الإقامات الجامعية
$residences_query = "SELECT * FROM residences ORDER BY name";
$residences_result = $conn->query($residences_query);

// تخزين الإقامات في مصفوفة للاستخدام لاحقاً
$residences = [];
if ($residences_result && $residences_result->num_rows > 0) {
    while($residence = $residences_result->fetch_assoc()) {
        $residences[] = $residence;
    }
}

// مسح جدول الطلبة
if (isset($_POST['clear_students'])) {
    // التحقق من وجود طلبات مرتبطة بالطلبة
    $check_requests = "SELECT COUNT(*) as count FROM requests";
    $result_check = $conn->query($check_requests);
    $row_check = $result_check->fetch_assoc();

    if ($row_check['count'] > 0) {
        // إذا كانت هناك طلبات مرتبطة، لا يمكن حذف الطلبة
        $message = "لا يمكن مسح جدول الطلبة لأن هناك طلبات مرتبطة بالطلبة. يجب حذف الطلبات أولاً.";
        $message_type = "danger";
    } else {
        // استخدام DELETE بدلاً من TRUNCATE لتجنب مشكلة الـ foreign key
        $sql = "DELETE FROM students";
        
        if ($conn->query($sql) === TRUE) {
            // إعادة ضبط قيمة AUTO_INCREMENT
            $reset_auto_increment = "ALTER TABLE students AUTO_INCREMENT = 1";
            $conn->query($reset_auto_increment);
            
            $message = "تم مسح جدول الطلبة بنجاح.";
            $message_type = "success";
        } else {
            $message = "خطأ في مسح جدول الطلبة: " . $conn->error;
            $message_type = "danger";
        }
    }
}

// حذف طالب
if (isset($_GET['delete'])) {
    $student_id = $_GET['delete'];
    
    // التحقق مما إذا كان الطالب لديه طلبات مرتبطة
    $check_requests = "SELECT COUNT(*) as count FROM requests WHERE student_id = $student_id";
    $result_check = $conn->query($check_requests);
    $row_check = $result_check->fetch_assoc();
    
    if ($row_check['count'] > 0) {
        $message = "لا يمكن حذف الطالب لأن لديه طلبات مرتبطة. يجب حذف الطلبات أولاً.";
        $message_type = "danger";
    } else {
        $sql = "DELETE FROM students WHERE id = $student_id";
        if ($conn->query($sql) === TRUE) {
            $message = "تم حذف الطالب بنجاح";
            $message_type = "success";
        } else {
            $message = "خطأ في حذف الطالب: " . $conn->error;
            $message_type = "danger";
        }
    }
}

// معالجة إضافة طالب جديد
if (isset($_POST['add_student'])) {
    $baccalaureate_number = $conn->real_escape_string($_POST['baccalaureate_number']);
    $first_name = $conn->real_escape_string($_POST['first_name']);
    $last_name = $conn->real_escape_string($_POST['last_name']);
    $birth_date = $conn->real_escape_string($_POST['birth_date']);
    $birth_place = $conn->real_escape_string($_POST['birth_place']);
    $residence = $conn->real_escape_string($_POST['residence']);
    $specialization = $conn->real_escape_string($_POST['specialization']);
    $study_year = $conn->real_escape_string($_POST['study_year']);
    $gender = $conn->real_escape_string($_POST['gender']); // إضافة متغير الجنس
    
    // التحقق من عدم وجود طالب بنفس رقم البكالوريا
    $check_sql = "SELECT * FROM students WHERE baccalaureate_number = '$baccalaureate_number'";
    $check_result = $conn->query($check_sql);
    
    if ($check_result->num_rows > 0) {
        $message = "خطأ: يوجد طالب مسجل بنفس رقم البكالوريا";
        $message_type = "danger";
    } else {
        $sql = "INSERT INTO students (baccalaureate_number, first_name, last_name, birth_date, birth_place, residence, specialization, study_year, gender) 
                VALUES ('$baccalaureate_number', '$first_name', '$last_name', '$birth_date', '$birth_place', '$residence', '$specialization', '$study_year', '$gender')";
        
        if ($conn->query($sql) === TRUE) {
            $message = "تم إضافة الطالب بنجاح";
            $message_type = "success";
        } else {
            $message = "خطأ في إضافة الطالب: " . $conn->error;
            $message_type = "danger";
        }
    }
}

// معالجة تعديل بيانات طالب
if (isset($_POST['edit_student'])) {
    $student_id = $conn->real_escape_string($_POST['student_id']);
    $baccalaureate_number = $conn->real_escape_string($_POST['baccalaureate_number']);
    $first_name = $conn->real_escape_string($_POST['first_name']);
    $last_name = $conn->real_escape_string($_POST['last_name']);
    $birth_date = $conn->real_escape_string($_POST['birth_date']);
    $birth_place = $conn->real_escape_string($_POST['birth_place']);
    $residence = $conn->real_escape_string($_POST['residence']);
    $specialization = $conn->real_escape_string($_POST['specialization']);
    $study_year = $conn->real_escape_string($_POST['study_year']);
    $gender = $conn->real_escape_string($_POST['gender']); // إضافة متغير الجنس
    
    // التحقق من عدم وجود طالب آخر بنفس رقم البكالوريا
    $check_sql = "SELECT * FROM students WHERE baccalaureate_number = '$baccalaureate_number' AND id != $student_id";
    $check_result = $conn->query($check_sql);
    
    if ($check_result->num_rows > 0) {
        $message = "خطأ: يوجد طالب آخر مسجل بنفس رقم البكالوريا";
        $message_type = "danger";
    } else {
        $sql = "UPDATE students SET 
                baccalaureate_number = '$baccalaureate_number', 
                first_name = '$first_name', 
                last_name = '$last_name', 
                birth_date = '$birth_date', 
                birth_place = '$birth_place', 
                residence = '$residence', 
                specialization = '$specialization', 
                study_year = '$study_year',
                gender = '$gender'
                WHERE id = $student_id";
        
        if ($conn->query($sql) === TRUE) {
            $message = "تم تحديث بيانات الطالب بنجاح";
            $message_type = "success";
        } else {
            $message = "خطأ في تحديث بيانات الطالب: " . $conn->error;
            $message_type = "danger";
        }
    }
}

// الإعدادات الافتراضية للتصفح بالصفحات
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$rows_per_page = isset($_GET['rows_per_page']) ? (int)$_GET['rows_per_page'] : 10;

// التأكد من أن القيم منطقية
if ($page < 1) $page = 1;
if (!in_array($rows_per_page, [10, 25, 50, 100])) $rows_per_page = 10;

// حساب موقع البداية للاستعلام
$offset = ($page - 1) * $rows_per_page;

// بناء استعلام البحث
$search_query = "";
if (isset($_GET['search_field']) && isset($_GET['search_value']) && !empty($_GET['search_value'])) {
    $search_field = $_GET['search_field'];
    $search_value = $conn->real_escape_string($_GET['search_value']);
    
    // تحديد الحقل المناسب للبحث
    switch ($search_field) {
        case 'baccalaureate_number':
            $search_query = " WHERE baccalaureate_number LIKE '%$search_value%'";
            break;
        case 'last_name':
            $search_query = " WHERE last_name LIKE '%$search_value%'";
            break;
        case 'first_name':
            $search_query = " WHERE first_name LIKE '%$search_value%'";
            break;
        case 'birth_date':
            $search_query = " WHERE birth_date LIKE '%$search_value%'";
            break;
        case 'birth_place':
            $search_query = " WHERE birth_place LIKE '%$search_value%'";
            break;
        case 'specialization':
            $search_query = " WHERE specialization LIKE '%$search_value%'";
            break;
        case 'study_year':
            $search_query = " WHERE study_year LIKE '%$search_value%'";
            break;
        case 'residence':
            $search_query = " WHERE residence LIKE '%$search_value%'";
            break;
        case 'gender':
            $search_query = " WHERE gender LIKE '%$search_value%'";
            break;
    }
}

// استرجاع العدد الإجمالي للطلبة (للتصفح بالصفحات)
$count_sql = "SELECT COUNT(*) as total FROM students";
if (!empty($search_query)) {
    $count_sql .= $search_query;
}
$count_result = $conn->query($count_sql);
$total_rows = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_rows / $rows_per_page);

// استرجاع الطلبة مع التصفح بالصفحات
$sql = "SELECT * FROM students";

// إضافة شرط البحث إذا وجد
if (!empty($search_query)) {
    $sql .= $search_query;
} 

// إضافة الترتيب
$sql .= " ORDER BY last_name, first_name";

// إضافة حدود التصفح بالصفحات
$sql .= " LIMIT $offset, $rows_per_page";

$result = $conn->query($sql);

// الحصول على بيانات طالب محدد للتعديل
$edit_student = null;
if (isset($_GET['edit'])) {
    $edit_id = $_GET['edit'];
    $edit_sql = "SELECT * FROM students WHERE id = $edit_id";
    $edit_result = $conn->query($edit_sql);
    if ($edit_result->num_rows > 0) {
        $edit_student = $edit_result->fetch_assoc();
    }
}

// الحصول على معلومات المسؤول
$admin_username = $_SESSION['admin']['username'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبة</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #007bff;
        }
        .main-content {
            padding: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .form-card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .form-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .student-table {
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }
        .gender-badge {
            font-size: 0.85rem;
            padding: 5px 10px;
            border-radius: 50px;
        }
        .gender-male {
            background-color: #cce5ff;
            color: #004085;
        }
        .gender-female {
            background-color: #f8d7da;
            color: #721c24;
        }
        .user-badge {
            background-color: #f8f9fa;
            border-radius: 50px;
            padding: 5px 15px;
            font-size: 0.9rem;
            color: #343a40;
            border: 1px solid #dee2e6;
        }
        /* تحسينات الشريط الجانبي */
        .sidebar {
            background-color: #343a40;
            min-height: 100vh;
            color: white;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.2rem 0.5rem;
            border-radius: 5px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(-3px);
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .sidebar .nav-link i {
            margin-left: 8px;
            width: 20px;
            text-align: center;
        }
        .sidebar-header {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar-section {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar-heading {
            font-size: 0.85rem;
            text-transform: uppercase;
            padding: 1rem;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="sidebar-header text-center">
                    <i class="fas fa-university fa-3x mb-3 text-primary"></i>
                    <h5 class="fw-bold">منصة التحويلات</h5>
                    <div class="small opacity-75 mb-3">لوحة تحكم المدير</div>
                    <div class="user-badge d-inline-block">
                            <i class="fas fa-user-shield me-1"></i> <?php echo $admin_username; ?>
                    </div>
                </div>
                    
                <div class="px-2">
                    <div class="sidebar-heading">القائمة الرئيسية</div>
                    <ul class="nav flex-column">
                    <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_requests.php">
                                <i class="fas fa-exchange-alt"></i>
                            إدارة الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                            <a class="nav-link active" href="manage_students.php">
                                <i class="fas fa-user-graduate"></i>
                            إدارة الطلبة
                        </a>
                    </li>
                    <li class="nav-item">
                            <a class="nav-link" href="manage_residences.php">
                                <i class="fas fa-building"></i>
                            إدارة الإقامات
                        </a>
                    </li>
                    </ul>
                    
                    <div class="sidebar-heading">إدارة النظام</div>
                    <ul class="nav flex-column">
                    <li class="nav-item">
                            <a class="nav-link" href="create_account.php">
                                <i class="fas fa-user-plus"></i>
                            إنشاء حساب مكلف
                        </a>
                    </li>
                    <li class="nav-item">
                            <a class="nav-link" href="manage_accounts.php">
                                <i class="fas fa-users-cog"></i>
                            إدارة الحسابات
                        </a>
                    </li>
                    </ul>
                    
                    <div class="sidebar-section">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link text-danger" href="logout.php">
                                    <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة الطلبة</h1>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : ($message_type == 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <!-- نموذج إضافة/تعديل طالب -->
                <div class="card form-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-<?php echo $edit_student ? 'edit' : 'plus-circle'; ?> me-2"></i>
                            <?php echo $edit_student ? 'تعديل بيانات طالب' : 'إضافة طالب جديد'; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="manage_students.php" method="POST" class="row g-3">
                            <?php if ($edit_student): ?>
                                <input type="hidden" name="student_id" value="<?php echo $edit_student['id']; ?>">
                            <?php endif; ?>
                            
                            <div class="col-md-4">
                                <label for="baccalaureate_number" class="form-label">رقم البكالوريا:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                    <input type="text" class="form-control" id="baccalaureate_number" name="baccalaureate_number" required value="<?php echo $edit_student ? $edit_student['baccalaureate_number'] : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="last_name" class="form-label">اللقب:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required value="<?php echo $edit_student ? $edit_student['last_name'] : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="first_name" class="form-label">الاسم:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required value="<?php echo $edit_student ? $edit_student['first_name'] : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="birth_date" class="form-label">تاريخ الميلاد:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date" required value="
                                    <?php //date_default_timezone_set('Africa/Casablanca');
                                     echo $edit_student ? date('Y-m-d', strtotime($edit_student['birth_date'])) : ''; 
                                    ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="birth_place" class="form-label">مكان الميلاد:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                    <input type="text" class="form-control" id="birth_place" name="birth_place" required value="<?php echo $edit_student ? $edit_student['birth_place'] : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="gender" class="form-label">الجنس:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value="ذكر" <?php echo ($edit_student && $edit_student['gender'] == 'ذكر') ? 'selected' : ''; ?>>ذكر</option>
                                        <option value="انثى" <?php echo ($edit_student && $edit_student['gender'] == 'انثى') ? 'selected' : ''; ?>>انثى</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="residence" class="form-label">الإقامة الحالية:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-building"></i></span>
                                    <select class="form-select" id="residence" name="residence" required>
                                        <option value="">-- اختر الإقامة --</option>
                                        <?php foreach ($residences as $residence): ?>
                                            <option value="<?php echo $residence['name']; ?>" <?php echo ($edit_student && $edit_student['residence'] == $residence['name']) ? 'selected' : ''; ?>>
                                                <?php echo $residence['name'] . ' (' . $residence['gender'] . ')'; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="specialization" class="form-label">التخصص:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-graduation-cap"></i></span>
                                    <input type="text" class="form-control" id="specialization" name="specialization" required value="<?php echo $edit_student ? $edit_student['specialization'] : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="study_year" class="form-label">السنة الدراسية:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-book"></i></span>
                                    <input type="text" class="form-control" id="study_year" name="study_year" required value="<?php echo $edit_student ? $edit_student['study_year'] : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="col-12 mt-3">
                                <?php if ($edit_student): ?>
                                    <button type="submit" name="edit_student" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث بيانات الطالب
                                    </button>
                                    <a href="manage_students.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="add_student" class="btn btn-success">
                                        <i class="fas fa-plus-circle me-1"></i> إضافة طالب
                                    </button>
                                    <!-- إضافة زر استيراد من ملف Excel -->
                                    <a href="#" class="btn btn-info ms-2" data-bs-toggle="modal" data-bs-target="#importExcelModal">
                                        <i class="fas fa-file-excel me-1"></i> استيراد من Excel
                                    </a>
                                    <!-- إضافة زر مسح جدول الطلبة -->
                                    <a href="#" class="btn btn-danger ms-2" data-bs-toggle="modal" data-bs-target="#clearStudentsModal">
                                        <i class="fas fa-trash-alt me-1"></i> مسح جدول الطلبة
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- قسم البحث والجدول -->
                <div class="card student-table">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الطلبة
                        </h5>
                        <span class="badge bg-primary">
                            <?php echo $result->num_rows; ?> طالب
                        </span>
                    </div>
                    <div class="card-body">
                        <!-- نموذج البحث -->
                        <form class="row g-3 mb-4" method="GET" action="">
                            <div class="col-md-4">
                                <label for="search_field" class="form-label">البحث حسب:</label>
                                <select class="form-select" name="search_field" id="search_field">
                                    <option value="baccalaureate_number" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'baccalaureate_number') ? 'selected' : ''; ?>>رقم البكالوريا</option>
                                    <option value="last_name" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'last_name') ? 'selected' : ''; ?>>اللقب</option>
                                    <option value="first_name" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'first_name') ? 'selected' : ''; ?>>الاسم</option>
                                    <option value="birth_date" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'birth_date') ? 'selected' : ''; ?>>تاريخ الميلاد</option>
                                    <option value="birth_place" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'birth_place') ? 'selected' : ''; ?>>مكان الميلاد</option>
                                    <option value="gender" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'gender') ? 'selected' : ''; ?>>الجنس</option>
                                    <option value="specialization" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'specialization') ? 'selected' : ''; ?>>التخصص</option>
                                    <option value="study_year" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'study_year') ? 'selected' : ''; ?>>السنة الدراسية</option>
                                    <option value="residence" <?php echo (isset($_GET['search_field']) && $_GET['search_field'] == 'residence') ? 'selected' : ''; ?>>الإقامة الحالية</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="search_value" class="form-label">قيمة البحث:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" name="search_value" id="search_value" placeholder="أدخل قيمة البحث" value="<?php echo isset($_GET['search_value']) ? htmlspecialchars($_GET['search_value']) : ''; ?>">
                                    <button type="submit" class="btn btn-primary">بحث</button>
                                </div>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <a href="manage_students.php" class="btn btn-secondary w-100">
                                    <i class="fas fa-redo me-1"></i> إعادة ضبط
                                </a>
                            </div>
                        </form>
                        
                        <!-- جدول الطلبة -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">رقم البكالوريا</th>
                                        <th scope="col">الاسم واللقب</th>
                                        <th scope="col">تاريخ الميلاد</th>
                                        <th scope="col">مكان الميلاد</th>
                                        <th scope="col">الجنس</th>
                                        <th scope="col">الإقامة الحالية</th>
                                        <th scope="col">التخصص</th>
                                        <th scope="col">السنة الدراسية</th>
                                        <th scope="col">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if ($result->num_rows > 0):
                                        while ($row = $result->fetch_assoc()): 
                                    ?>
                                    <tr>
                                        <td><?php echo $row['id']; ?></td>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-id-card me-1"></i>
                                                <?php echo $row['baccalaureate_number']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?php echo $row['last_name'] . ' ' . $row['first_name']; ?></strong>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($row['birth_date'])); ?></td>
                                        <td><?php echo $row['birth_place']; ?></td>
                                        <td>
                                            <span class="gender-badge <?php echo $row['gender'] == 'ذكر' ? 'gender-male' : 'gender-female'; ?>">
                                                <i class="fas fa-<?php echo $row['gender'] == 'ذكر' ? 'male' : 'female'; ?> me-1"></i>
                                                <?php echo $row['gender']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info text-dark">
                                                <i class="fas fa-building me-1"></i>
                                                <?php echo $row['residence']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $row['specialization']; ?></td>
                                        <td>
                                        <span class="badge bg-light text-dark">
                                                <?php echo $row['study_year']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="?edit=<?php echo $row['id']; ?>" class="btn btn-warning">
                                                    <i class="fas fa-edit me-1"></i> تعديل
                                                </a>
                                                <a href="?delete=<?php echo $row['id']; ?>" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الطالب؟ لن تتمكن من حذفه إذا كان لديه طلبات مرتبطة.')">
                                                    <i class="fas fa-trash-alt me-1"></i> حذف
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php 
                                        endwhile; 
                                    else: 
                                    ?>
                                    <tr>
                                        <td colspan="10" class="text-center py-4">
                                            <div class="alert alert-info mb-0">
                                                <i class="fas fa-info-circle me-2"></i> لا توجد بيانات طلبة مطابقة للبحث
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- التصفح بالصفحات والتحكم بعدد الصفوف -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <!-- اختيار عدد الصفوف لكل صفحة -->
                            <div class="d-flex align-items-center">
                                <span class="me-2">عرض</span>
                                <select class="form-select form-select-sm" id="rowsPerPage" onchange="changeRowsPerPage()">
                                    <option value="10" <?php echo $rows_per_page == 10 ? 'selected' : ''; ?>>10</option>
                                    <option value="25" <?php echo $rows_per_page == 25 ? 'selected' : ''; ?>>25</option>
                                    <option value="50" <?php echo $rows_per_page == 50 ? 'selected' : ''; ?>>50</option>
                                    <option value="100" <?php echo $rows_per_page == 100 ? 'selected' : ''; ?>>100</option>
                                </select>
                                <span class="ms-2">صف</span>
                            </div>
                            
                            <!-- معلومات عن الصفحات -->
                            <div class="text-muted">
                                عرض <strong><?php echo min($offset + 1, $total_rows); ?>-<?php echo min($offset + $rows_per_page, $total_rows); ?></strong> من إجمالي <strong><?php echo $total_rows; ?></strong> طالب
                            </div>
                            
                            <?php if ($total_pages > 1): ?>
                            <!-- أزرار التنقل بين الصفحات -->
                            <nav aria-label="تنقل بين الصفحات">
                                <ul class="pagination mb-0">
                                    <!-- زر الصفحة السابقة -->
                                    <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="<?php echo constructPageUrl($page - 1); ?>" aria-label="السابق">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    
                                    <?php
                                    // عرض أرقام الصفحات
                                    $start_page = max(1, $page - 2);
                                    $end_page = min($start_page + 4, $total_pages);
                                    
                                    // التأكد من عرض 5 صفحات دائمًا إذا كان ذلك ممكنًا
                                    if ($end_page - $start_page < 4 && $total_pages > 4) {
                                        $start_page = max(1, $end_page - 4);
                                    }
                                    
                                    // عرض الصفحة الأولى دائمًا
                                    if ($start_page > 1) {
                                        echo '<li class="page-item"><a class="page-link" href="' . constructPageUrl(1) . '">1</a></li>';
                                        if ($start_page > 2) {
                                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                        }
                                    }
                                    
                                    // عرض أرقام الصفحات
                                    for ($i = $start_page; $i <= $end_page; $i++) {
                                        echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '">';
                                        echo '<a class="page-link" href="' . constructPageUrl($i) . '">' . $i . '</a>';
                                        echo '</li>';
                                    }
                                    
                                    // عرض الصفحة الأخيرة دائمًا
                                    if ($end_page < $total_pages) {
                                        if ($end_page < $total_pages - 1) {
                                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                        }
                                        echo '<li class="page-item"><a class="page-link" href="' . constructPageUrl($total_pages) . '">' . $total_pages . '</a></li>';
                                    }
                                    ?>
                                    
                                    <!-- زر الصفحة التالية -->
                                    <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="<?php echo constructPageUrl($page + 1); ?>" aria-label="التالي">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- نافذة استيراد ملف Excel -->
    <div class="modal fade" id="importExcelModal" tabindex="-1" aria-labelledby="importExcelModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importExcelModalLabel">استيراد بيانات الطلبة من ملف Excel</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="import_students.php" method="post" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="excelFile" class="form-label">اختر ملف Excel:</label>
                            <input class="form-control" type="file" id="excelFile" name="excelFile" accept=".xlsx, .xls" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i> يجب أن يحتوي الملف على الأعمدة التالية: رقم البكالوريا، اللقب، الاسم، تاريخ الميلاد، مكان الميلاد، الجنس، الإقامة الحالية، التخصص، السنة الدراسية.
                            </div>
                        </div>
                        
                        <!-- إضافة اختيار الجنس -->
                        <div class="mb-3">
                            <label for="importGender" class="form-label">تحديد الجنس للطلبة المستوردين:</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                                <select class="form-select" id="importGender" name="importGender">
                                    <option value="">-- استخدام القيمة من ملف Excel --</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="انثى">انثى</option>
                                </select>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i> إذا تم تحديد قيمة، سيتم استخدامها لجميع الطلبة المستوردين بغض النظر عن القيمة في ملف Excel.
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-import me-1"></i> استيراد البيانات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد مسح جدول الطلبة -->
    <div class="modal fade" id="clearStudentsModal" tabindex="-1" aria-labelledby="clearStudentsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="clearStudentsModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i> تأكيد مسح جدول الطلبة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="manage_students.php" method="post">
                    <div class="modal-body">
                        <p><strong>تحذير:</strong> هل أنت متأكد من رغبتك في مسح جميع بيانات الطلبة؟</p>
                        <p class="text-danger">لا يمكن التراجع عن هذا الإجراء. سيتم حذف جميع سجلات الطلبة بشكل دائم.</p>
                        <p class="small text-muted"><i class="fas fa-info-circle me-1"></i> ملاحظة: لن يتم حذف الطلبة إذا كان لديهم طلبات تحويل مرتبطة بهم.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </button>
                        <button type="submit" name="clear_students" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> نعم، قم بالمسح
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <?php
    // دالة لبناء رابط الصفحة مع الحفاظ على معلمات البحث
    function constructPageUrl($page_number) {
        $params = $_GET;
        $params['page'] = $page_number;
        $params['rows_per_page'] = isset($_GET['rows_per_page']) ? $_GET['rows_per_page'] : 10;
        return '?' . http_build_query($params);
    }
    ?>
    
    <script>
        // تغيير عدد الصفوف لكل صفحة
        function changeRowsPerPage() {
            const rowsPerPage = document.getElementById('rowsPerPage').value;
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('rows_per_page', rowsPerPage);
            currentUrl.searchParams.set('page', 1); // إعادة التعيين إلى الصفحة الأولى
            window.location.href = currentUrl.toString();
        }
        
        // تحقق من تطابق جنس الطالب مع جنس الإقامة
        document.getElementById('residence').addEventListener('change', function() {
            const selectedResidence = this.value;
            const selectedGender = document.getElementById('gender').value;
            
            // البحث عن الإقامة المحددة
            const residences = <?php echo json_encode($residences); ?>;
            const residence = residences.find(r => r.name === selectedResidence);
            
            if (residence) {
                // التحقق من تطابق الجنس
                const residenceGender = residence.gender;
                const isMatch = (residenceGender === 'ذكور' && selectedGender === 'ذكر') || 
                               (residenceGender === 'اناث' && selectedGender === 'انثى');
                
                if (!isMatch) {
                    alert('تنبيه: جنس الطالب لا يتطابق مع جنس الإقامة المحددة!');
                }
            }
        });
        
        // تحديث قائمة الإقامات عند تغيير الجنس
        document.getElementById('gender').addEventListener('change', function() {
            const selectedGender = this.value;
            const residenceSelect = document.getElementById('residence');
            const residences = <?php echo json_encode($residences); ?>;
            
            // حفظ القيمة المحددة حالياً
            const currentValue = residenceSelect.value;
            
            // إفراغ القائمة
            residenceSelect.innerHTML = '<option value="">-- اختر الإقامة --</option>';
            
            // إضافة الإقامات المناسبة للجنس
            residences.forEach(residence => {
                const residenceGender = residence.gender;
                const isMatch = (residenceGender === 'ذكور' && selectedGender === 'ذكر') || 
                               (residenceGender === 'اناث' && selectedGender === 'انثى');
                
                if (isMatch) {
                    const option = document.createElement('option');
                    option.value = residence.name;
                    option.textContent = residence.name + ' (' + residence.gender + ')';
                    
                    // إعادة تحديد القيمة السابقة إذا كانت مناسبة
                    if (currentValue === residence.name) {
                        option.selected = true;
                    }
                    
                    residenceSelect.appendChild(option);
                }
            });
        });
    </script>
</body>
</html>

<?php
$conn->close();
?>