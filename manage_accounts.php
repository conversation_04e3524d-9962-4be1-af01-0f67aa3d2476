<?php
session_start();

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

include 'db_connect.php';

$message = ""; // متغير لتخزين الرسائل
$message_type = ""; // متغير لتخزين نوع الرسالة
$edit_account = null; // متغير لتخزين بيانات الحساب المراد تعديله

// استرجاع قائمة الإقامات الجامعية
$residences_query = "SELECT * FROM residences ORDER BY name";
$residences_result = $conn->query($residences_query);
$residences = [];
if ($residences_result && $residences_result->num_rows > 0) {
    while ($row = $residences_result->fetch_assoc()) {
        $residences[] = $row;
    }
}

// تعديل حساب
if (isset($_GET['edit'])) {
    $id = $_GET['edit'];
    // استخدام الاستعلامات المُجهزة لمنع هجمات SQL Injection
    $stmt = $conn->prepare("SELECT * FROM residence_managers WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result_edit = $stmt->get_result();
    if ($result_edit->num_rows > 0) {
        $edit_account = $result_edit->fetch_assoc();
    }
}

// حفظ التعديلات
if (isset($_POST['update_account'])) {
    $id = $_POST['account_id'];
    $username = $_POST['username'];
    $residence_name = $_POST['residence_name'];
    
    // تحديث كلمة المرور فقط إذا تم إدخالها
    if (!empty($_POST['password'])) {
        $password = $_POST['password'];
        // تشفير كلمة المرور باستخدام password_hash
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // استخدام الاستعلامات المُجهزة لمنع هجمات SQL Injection
        $stmt = $conn->prepare("UPDATE residence_managers SET username = ?, password = ?, residence_name = ? WHERE id = ?");
        $stmt->bind_param("sssi", $username, $hashed_password, $residence_name, $id);
    } else {
        // استخدام الاستعلامات المُجهزة بدون تحديث كلمة المرور
        $stmt = $conn->prepare("UPDATE residence_managers SET username = ?, residence_name = ? WHERE id = ?");
        $stmt->bind_param("ssi", $username, $residence_name, $id);
    }
    
    if ($stmt->execute()) {
        $message = "تم تحديث الحساب بنجاح";
        $message_type = "success";
    } else {
        $message = "خطأ في تحديث الحساب: " . $stmt->error;
        $message_type = "danger";
    }
}

// حذف حساب
if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    
    // التحقق من أن الحساب ليس مديرًا قبل الحذف
    $stmt = $conn->prepare("SELECT role FROM residence_managers WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $check_role = $stmt->get_result();
    
    if ($check_role->num_rows > 0) {
        $role = $check_role->fetch_assoc()['role'];
        if ($role != 'manager') {
            $delete_stmt = $conn->prepare("DELETE FROM residence_managers WHERE id = ?");
            $delete_stmt->bind_param("i", $id);
            
            if ($delete_stmt->execute()) {
                $message = "تم حذف الحساب بنجاح";
                $message_type = "success";
            } else {
                $message = "خطأ في حذف الحساب: " . $conn->error;
                $message_type = "danger";
            }
        } else {
            $message = "لا يمكن حذف حسابات المديرين";
            $message_type = "warning";
        }
    }
}

// استخدام الاستعلامات المُجهزة لاسترجاع جميع الحسابات
$result = $conn->query("SELECT * FROM residence_managers");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحسابات</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #007bff;
        }
        .main-content {
            padding: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .edit-form-card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        .password-toggle {
            cursor: pointer;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
        .user-badge {
            background-color: #f8f9fa;
            border-radius: 50px;
            padding: 5px 15px;
            font-size: 0.9rem;
            color: #343a40;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse p-0">
                <div class="text-center p-3 mb-3 border-bottom">
                    <h5 class="mb-0">منصة التحويلات</h5>
                    <p class="small mb-0">لوحة تحكم المدير</p>
                    <div class="mt-2">
                        <span class="user-badge">
                            <i class="fas fa-user-shield me-1"></i> <?php echo $_SESSION['admin']['username']; ?>
                        </span>
                    </div>
                </div>
                <ul class="nav flex-column p-2">
                    
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_requests.php">
                            <i class="fas fa-exchange-alt me-2"></i>
                            إدارة الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_students.php">
                            <i class="fas fa-user-graduate me-2"></i>
                            إدارة الطلبة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="manage_residences.php">
                            <i class="fas fa-building me-2"></i>
                            إدارة الإقامات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link rounded" href="create_account.php">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب مكلف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active rounded" href="manage_accounts.php">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة الحسابات
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link rounded bg-danger text-white" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة حسابات المكلفين بالإقامات</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="create_account.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-user-plus me-1"></i> إضافة مكلف جديد
                        </a>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : ($message_type == 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($edit_account): ?>
                    <div class="card edit-form-card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-user-edit me-2"></i>
                                تعديل حساب المكلف بالإقامة
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <form method="POST" action="">
                                <input type="hidden" name="account_id" value="<?php echo $edit_account['id']; ?>">
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="username" class="form-label">اسم المستخدم:</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-user"></i>
                                            </span>
                                            <input type="text" class="form-control" id="username" name="username" value="<?php echo $edit_account['username']; ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="residence_name" class="form-label">الإقامة الجامعية:</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-building"></i>
                                            </span>
                                            <select class="form-select" id="residence_name" name="residence_name" required>
                                                <option value="">-- اختر الإقامة الجامعية --</option>
                                                <?php foreach ($residences as $residence): ?>
                                                    <option value="<?php echo $residence['name']; ?>" <?php echo ($edit_account['residence_name'] == $residence['name']) ? 'selected' : ''; ?>>
                                                        <?php echo $residence['name'] . ' (' . $residence['gender'] . ')'; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="password" class="form-label">كلمة المرور (اتركها فارغة إذا لم ترغب في تغييرها):</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" name="password">
                                        <span class="input-group-text password-toggle" onclick="togglePasswordVisibility()">
                                            <i class="fas fa-eye" id="togglePassword"></i>
                                        </span>
                                    </div>
                                    <div class="form-text">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير كلمة المرور</div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" name="update_account" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث الحساب
                                    </button>
                                    <a href="manage_accounts.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">قائمة الحسابات</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">اسم المستخدم</th>
                                        <th scope="col">الدور</th>
                                        <th scope="col">اسم الإقامة الجامعية</th>
                                        <th scope="col">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($result->num_rows > 0): ?>
                                        <?php while ($row = $result->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $row['id']; ?></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-<?php echo $row['role'] == 'manager' ? 'user-shield text-primary' : 'user text-secondary'; ?> me-2"></i>
                                                        <?php echo $row['username']; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo $row['role'] == 'manager' ? 'bg-primary' : 'bg-info'; ?>">
                                                        <?php echo $row['role'] == 'manager' ? 'مدير' : 'مكلف بالإقامة'; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo isset($row['residence_name']) ? $row['residence_name'] : '-'; ?></td>
                                                <td>
                                                    <?php if ($row['role'] != 'manager'): ?>
                                                        <div class="btn-group btn-group-sm" role="group">
                                                            <a href="?edit=<?php echo $row['id']; ?>" class="btn btn-warning">
                                                                <i class="fas fa-edit"></i> تعديل
                                                            </a>
                                                            <a href="?delete=<?php echo $row['id']; ?>" onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')" class="btn btn-danger">
                                                                <i class="fas fa-trash-alt"></i> حذف
                                                            </a>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">لا يمكن التعديل</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i> لا توجد حسابات مسجلة
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- سكريبت لإظهار/إخفاء كلمة المرور -->
    <script>
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('togglePassword');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>

<?php
$conn->close();
?>
