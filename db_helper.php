<?php
/**
 * ملف مساعد لوظائف قاعدة البيانات
 * يحتوي على وظائف مساعدة للتعامل مع قاعدة البيانات بشكل آمن وسهل
 */

include_once 'db_connect.php';

/**
 * تنفيذ استعلام آمن مع تفادي SQL Injection
 * 
 * @param string $query الاستعلام مع علامات استفهام للقيم
 * @param string $types أنواع المعاملات (s للنصوص، i للأرقام الصحيحة، d للأرقام العشرية، b للثنائية)
 * @param array $params مصفوفة القيم التي ستحل محل علامات الاستفهام
 * @return mysqli_stmt|false كائن الاستعلام أو خطأ
 */
function db_query($query, $types = "", $params = []) {
    global $conn;
    $stmt = $conn->prepare($query);
    
    if ($stmt === false) {
        return false;
    }
    
    if (!empty($types) && !empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    return $stmt;
}

/**
 * تنفيذ استعلام واسترجاع نتيجة واحدة فقط
 * 
 * @param string $query الاستعلام
 * @param string $types أنواع المعاملات
 * @param array $params القيم
 * @return array|null مصفوفة تحتوي على الصف الأول من النتائج أو null
 */
function db_fetch_row($query, $types = "", $params = []) {
    $stmt = db_query($query, $types, $params);
    
    if ($stmt === false) {
        return null;
    }
    
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    return $row;
}

/**
 * تنفيذ استعلام واسترجاع كل النتائج
 * 
 * @param string $query الاستعلام
 * @param string $types أنواع المعاملات
 * @param array $params القيم
 * @return array مصفوفة تحتوي على كل الصفوف
 */
function db_fetch_all($query, $types = "", $params = []) {
    $stmt = db_query($query, $types, $params);
    
    if ($stmt === false) {
        return [];
    }
    
    $result = $stmt->get_result();
    $rows = [];
    
    while ($row = $result->fetch_assoc()) {
        $rows[] = $row;
    }
    
    $stmt->close();
    
    return $rows;
}

/**
 * إدراج بيانات في قاعدة البيانات
 * 
 * @param string $table اسم الجدول
 * @param array $data مصفوفة تحتوي على البيانات [عمود => قيمة]
 * @return int|false معرّف السجل المضاف أو false في حالة الخطأ
 */
function db_insert($table, $data) {
    global $conn;
    
    $columns = array_keys($data);
    $values = array_values($data);
    $placeholders = array_fill(0, count($values), '?');
    
    $types = '';
    foreach ($values as $value) {
        if (is_int($value)) {
            $types .= 'i';
        } elseif (is_double($value)) {
            $types .= 'd';
        } elseif (is_string($value)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
    }
    
    $query = "INSERT INTO $table (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
    
    $stmt = db_query($query, $types, $values);
    
    if ($stmt === false) {
        return false;
    }
    
    $insert_id = $conn->insert_id;
    $stmt->close();
    
    return $insert_id;
}

/**
 * تحديث بيانات في قاعدة البيانات
 * 
 * @param string $table اسم الجدول
 * @param array $data مصفوفة تحتوي على البيانات المراد تحديثها [عمود => قيمة]
 * @param string $where شرط التحديث
 * @param string $where_types أنواع قيم شرط التحديث
 * @param array $where_params قيم شرط التحديث
 * @return bool true في حالة النجاح، false في حالة الفشل
 */
function db_update($table, $data, $where, $where_types = "", $where_params = []) {
    $set_clauses = [];
    $values = [];
    $types = '';
    
    foreach ($data as $column => $value) {
        $set_clauses[] = "$column = ?";
        $values[] = $value;
        
        if (is_int($value)) {
            $types .= 'i';
        } elseif (is_double($value)) {
            $types .= 'd';
        } elseif (is_string($value)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
    }
    
    $query = "UPDATE $table SET " . implode(', ', $set_clauses) . " WHERE $where";
    $types .= $where_types;
    $params = array_merge($values, $where_params);
    
    $stmt = db_query($query, $types, $params);
    
    if ($stmt === false) {
        return false;
    }
    
    $affected_rows = $stmt->affected_rows;
    $stmt->close();
    
    return $affected_rows > 0;
}

/**
 * حذف بيانات من قاعدة البيانات
 * 
 * @param string $table اسم الجدول
 * @param string $where شرط الحذف
 * @param string $types أنواع القيم
 * @param array $params قيم الشرط
 * @return bool true في حالة النجاح، false في حالة الفشل
 */
function db_delete($table, $where, $types = "", $params = []) {
    $query = "DELETE FROM $table WHERE $where";
    
    $stmt = db_query($query, $types, $params);
    
    if ($stmt === false) {
        return false;
    }
    
    $affected_rows = $stmt->affected_rows;
    $stmt->close();
    
    return $affected_rows > 0;
}

/**
 * الحصول على عدد الصفوف التي تطابق شرط معين
 * 
 * @param string $table اسم الجدول
 * @param string $where شرط الاستعلام (اختياري)
 * @param string $types أنواع القيم (اختياري)
 * @param array $params قيم الشرط (اختياري)
 * @return int عدد الصفوف
 */
function db_count($table, $where = "", $types = "", $params = []) {
    $query = "SELECT COUNT(*) as count FROM $table";
    
    if (!empty($where)) {
        $query .= " WHERE $where";
    }
    
    $result = db_fetch_row($query, $types, $params);
    
    return $result ? $result['count'] : 0;
}
?> 