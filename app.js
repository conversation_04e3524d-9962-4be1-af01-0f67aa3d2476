/**
 * الملف الرئيسي لجافا سكريبت لمنصة التحويلات بين الإقامات الجامعية
 */

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من الوضع المظلم في التخزين المحلي
    const isDarkMode = localStorage.getItem('darkMode') === 'true';
    
    // تطبيق الوضع المظلم إذا كان مفعلاً
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
    }
    
    // إضافة زر تبديل الوضع المظلم إذا لم يكن موجوداً
    if (!document.querySelector('.dark-mode-toggle')) {
        const darkModeToggle = document.createElement('button');
        darkModeToggle.className = 'dark-mode-toggle';
        darkModeToggle.setAttribute('title', isDarkMode ? 'تفعيل الوضع النهاري' : 'تفعيل الوضع الليلي');
        darkModeToggle.innerHTML = isDarkMode ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
        document.body.appendChild(darkModeToggle);
        
        // تفعيل تبديل الوضع المظلم
        darkModeToggle.addEventListener('click', toggleDarkMode);
    }
    
    // تفعيل التوجيه بالنقر على صفوف الجداول
    initTableRowLinks();
    
    // تفعيل رسائل النجاح المؤقتة
    initAlertDismissal();
    
    // تفعيل البحث التفاعلي
    initLiveSearch();
    
    // تفعيل التحميل التفاعلي للبيانات
    initAjaxLoading();
});

/**
 * تبديل الوضع المظلم
 */
function toggleDarkMode() {
    const body = document.body;
    const isDarkMode = body.classList.toggle('dark-mode');
    const darkModeToggle = document.querySelector('.dark-mode-toggle');
    
    // تحديث أيقونة الزر
    darkModeToggle.innerHTML = isDarkMode ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
    darkModeToggle.setAttribute('title', isDarkMode ? 'تفعيل الوضع النهاري' : 'تفعيل الوضع الليلي');
    
    // حفظ الإعداد في التخزين المحلي
    localStorage.setItem('darkMode', isDarkMode);
}

/**
 * تفعيل التوجيه بالنقر على صفوف الجداول
 */
function initTableRowLinks() {
    const tableRows = document.querySelectorAll('.table-row-link');
    
    tableRows.forEach(row => {
        row.addEventListener('click', function(e) {
            if (!e.target.closest('a, button, .no-link')) {
                const link = this.dataset.href;
                if (link) {
                    window.location.href = link;
                }
            }
        });
    });
}

/**
 * تفعيل إخفاء رسائل النجاح بعد فترة
 */
function initAlertDismissal() {
    const successAlerts = document.querySelectorAll('.alert-success');
    
    successAlerts.forEach(alert => {
        // إخفاء رسائل النجاح بعد 5 ثوان
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

/**
 * تفعيل البحث التفاعلي
 */
function initLiveSearch() {
    const searchInputs = document.querySelectorAll('.live-search-input');
    
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const searchValue = this.value.toLowerCase();
            const targetId = this.dataset.target;
            const items = document.querySelectorAll(`#${targetId} .live-search-item`);
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(searchValue) ? '' : 'none';
            });
        });
    });
}

/**
 * تفعيل التحميل التفاعلي للبيانات
 */
function initAjaxLoading() {
    const ajaxLinks = document.querySelectorAll('.ajax-link');
    
    ajaxLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.dataset.target;
            const url = this.href;
            const targetContainer = document.getElementById(targetId);
            
            if (targetContainer) {
                // عرض مؤشر التحميل
                targetContainer.innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div><p class="mt-3">جاري التحميل...</p></div>';
                
                // جلب البيانات
                fetch(url)
                    .then(response => response.text())
                    .then(data => {
                        targetContainer.innerHTML = data;
                        // إعادة تهيئة الدوال التفاعلية للمحتوى الجديد
                        initTableRowLinks();
                        initAlertDismissal();
                    })
                    .catch(error => {
                        targetContainer.innerHTML = `<div class="alert alert-danger">حدث خطأ أثناء تحميل البيانات: ${error.message}</div>`;
                    });
            }
        });
    });
}

/**
 * تحديث جدول الطلبات في الوقت الفعلي عند تغيير الحالة
 */
function updateRequestStatus(requestId, status) {
    // تعيين مؤشر التحميل
    const statusCell = document.querySelector(`#request-status-${requestId}`);
    if (statusCell) {
        statusCell.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">جاري التحديث...</span></div>';
    }
    
    // إرسال طلب AJAX لتحديث الحالة
    fetch('update_request_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `request_id=${requestId}&status=${status}&csrf_token=${document.querySelector('input[name="csrf_token"]').value}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث خلية الحالة
            if (statusCell) {
                let statusText = '';
                let statusClass = '';
                
                switch (status) {
                    case 'pending':
                        statusText = 'قيد الدراسة';
                        statusClass = 'bg-warning text-dark';
                        break;
                    case 'accepted':
                        statusText = 'مقبول';
                        statusClass = 'bg-success';
                        break;
                    case 'rejected':
                        statusText = 'مرفوض';
                        statusClass = 'bg-danger';
                        break;
                }
                
                statusCell.innerHTML = `<span class="badge ${statusClass}">${statusText}</span>`;
                
                // عرض رسالة نجاح
                showToast('تم تحديث حالة الطلب بنجاح', 'success');
            }
        } else {
            // عرض رسالة خطأ
            showToast(data.message || 'حدث خطأ أثناء تحديث حالة الطلب', 'danger');
        }
    })
    .catch(error => {
        showToast('حدث خطأ في الاتصال: ' + error.message, 'danger');
    });
}

/**
 * إظهار رسالة Toast
 */
function showToast(message, type = 'info') {
    // إنشاء عنصر Toast
    const toastContainer = document.querySelector('.toast-container') || (() => {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
        return container;
    })();
    
    const toastId = 'toast-' + Date.now();
    const toastElement = document.createElement('div');
    toastElement.className = `toast align-items-center text-white bg-${type} border-0`;
    toastElement.setAttribute('role', 'alert');
    toastElement.setAttribute('aria-live', 'assertive');
    toastElement.setAttribute('aria-atomic', 'true');
    toastElement.setAttribute('id', toastId);
    
    toastElement.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
        </div>
    `;
    
    toastContainer.appendChild(toastElement);
    
    // تفعيل وإظهار Toast
    const toast = new bootstrap.Toast(toastElement, {
        animation: true,
        autohide: true,
        delay: 5000
    });
    
    toast.show();
} 