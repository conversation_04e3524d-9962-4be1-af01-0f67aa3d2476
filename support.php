<?php
session_start();

$message = "";
$message_type = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $category = trim($_POST['category'] ?? '');
    $priority = trim($_POST['priority'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($name) || empty($email) || empty($category) || empty($priority) || empty($subject) || empty($description)) {
        $message = "يرجى ملء جميع الحقول المطلوبة.";
        $message_type = "danger";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = "يرجى إدخال بريد إلكتروني صحيح.";
        $message_type = "danger";
    } else {
        // إنشاء رقم تذكرة فريد
        $ticket_number = 'TKT-' . date('Ymd') . '-' . strtoupper(substr(md5(uniqid()), 0, 6));
        
        // في بيئة الإنتاج، هنا سيتم حفظ التذكرة في قاعدة البيانات
        // لأغراض التطوير، سنقوم فقط بعرض رسالة نجاح
        
        $message = "تم إنشاء تذكرة الدعم بنجاح! رقم التذكرة: <strong>{$ticket_number}</strong><br>سنقوم بالرد عليك في أقرب وقت ممكن.";
        $message_type = "success";
        
        // إعادة تعيين النموذج
        $_POST = array();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدعم الفني | منصة التحويلات بين الإقامات الجامعية</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .main-content {
            background: white;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            position: relative;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,100 1000,0 1000,100"/></svg>');
            background-size: cover;
        }

        .content-section {
            padding: 60px 0;
        }

        .support-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            height: 100%;
            transition: all 0.3s ease;
        }

        .support-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .support-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .btn-support {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-support:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .priority-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .priority-low {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .priority-medium {
            background-color: #fff3cd;
            color: #856404;
        }

        .priority-high {
            background-color: #f8d7da;
            color: #721c24;
        }

        .priority-urgent {
            background-color: #f5c6cb;
            color: #721c24;
        }

        .help-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .help-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .help-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .help-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-left: 15px;
            width: 40px;
            text-align: center;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-online {
            background-color: var(--success-color);
        }

        .status-busy {
            background-color: var(--warning-color);
        }

        .status-offline {
            background-color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h4 mb-0 text-white">
                    <i class="fas fa-headset me-2"></i>
                    الدعم الفني
                </h1>
                <a href="index.html" class="btn btn-outline-light">
                    <i class="fas fa-home me-1"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">الدعم الفني</h1>
            <p class="lead mb-0">فريق الدعم الفني جاهز لمساعدتك في حل أي مشكلة تقنية أو استفسار</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-section">
            <div class="container">
                <!-- Help Section -->
                <div class="help-section">
                    <h3 class="mb-4">
                        <i class="fas fa-question-circle me-2"></i>
                        كيف يمكننا مساعدتك؟
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="help-item">
                                <i class="fas fa-book help-icon"></i>
                                <div>
                                    <h6 class="mb-1">دليل المستخدم</h6>
                                    <p class="mb-0">دليل شامل لاستخدام المنصة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="help-item">
                                <i class="fas fa-video help-icon"></i>
                                <div>
                                    <h6 class="mb-1">فيديوهات تعليمية</h6>
                                    <p class="mb-0">شروحات مرئية للميزات المختلفة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="help-item">
                                <i class="fas fa-comments help-icon"></i>
                                <div>
                                    <h6 class="mb-1">الدردشة المباشرة</h6>
                                    <p class="mb-0">تواصل فوري مع فريق الدعم</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="help-item">
                                <i class="fas fa-ticket-alt help-icon"></i>
                                <div>
                                    <h6 class="mb-1">تذكرة دعم</h6>
                                    <p class="mb-0">إنشاء تذكرة للمشاكل المعقدة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support Form and Info -->
                <div class="row">
                    <!-- Support Form -->
                    <div class="col-lg-8 mb-4">
                        <div class="support-card">
                            <h3 class="mb-4">
                                <i class="fas fa-ticket-alt me-2"></i>
                                إنشاء تذكرة دعم فني
                            </h3>
                            
                            <?php if (!empty($message)): ?>
                                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                                    <?php if ($message_type == "danger"): ?>
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-check-circle me-2"></i>
                                    <?php endif; ?>
                                    <?php echo $message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>
                            
                            <form action="support.php" method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo $_POST['name'] ?? ''; ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo $_POST['email'] ?? ''; ?>" required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo $_POST['phone'] ?? ''; ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="category" class="form-label">نوع المشكلة *</label>
                                        <select class="form-select" id="category" name="category" required>
                                            <option value="">اختر نوع المشكلة</option>
                                            <option value="technical" <?php echo ($_POST['category'] ?? '') === 'technical' ? 'selected' : ''; ?>>مشكلة تقنية</option>
                                            <option value="account" <?php echo ($_POST['category'] ?? '') === 'account' ? 'selected' : ''; ?>>مشكلة في الحساب</option>
                                            <option value="request" <?php echo ($_POST['category'] ?? '') === 'request' ? 'selected' : ''; ?>>مشكلة في الطلبات</option>
                                            <option value="payment" <?php echo ($_POST['category'] ?? '') === 'payment' ? 'selected' : ''; ?>>مشكلة في الدفع</option>
                                            <option value="other" <?php echo ($_POST['category'] ?? '') === 'other' ? 'selected' : ''; ?>>أخرى</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="priority" class="form-label">أولوية المشكلة *</label>
                                        <select class="form-select" id="priority" name="priority" required onchange="updatePriorityBadge()">
                                            <option value="">اختر الأولوية</option>
                                            <option value="low" <?php echo ($_POST['priority'] ?? '') === 'low' ? 'selected' : ''; ?>>منخفضة</option>
                                            <option value="medium" <?php echo ($_POST['priority'] ?? '') === 'medium' ? 'selected' : ''; ?>>متوسطة</option>
                                            <option value="high" <?php echo ($_POST['priority'] ?? '') === 'high' ? 'selected' : ''; ?>>عالية</option>
                                            <option value="urgent" <?php echo ($_POST['priority'] ?? '') === 'urgent' ? 'selected' : ''; ?>>عاجلة</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">مستوى الأولوية</label>
                                        <div id="priority-badge" class="priority-badge" style="display: none;">
                                            <span id="priority-text"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">عنوان المشكلة *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="<?php echo $_POST['subject'] ?? ''; ?>" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="description" class="form-label">وصف المشكلة *</label>
                                    <textarea class="form-control" id="description" name="description" rows="6" 
                                              placeholder="يرجى وصف المشكلة بالتفصيل..." required><?php echo $_POST['description'] ?? ''; ?></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-support">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال تذكرة الدعم
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Support Info -->
                    <div class="col-lg-4">
                        <div class="support-card">
                            <h3 class="mb-4">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الدعم الفني
                            </h3>
                            
                            <div class="mb-4">
                                <h6 class="mb-3">حالة فريق الدعم:</h6>
                                <div class="d-flex align-items-center mb-2">
                                    <span class="status-indicator status-online"></span>
                                    <span>متصل الآن</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <span class="status-indicator status-busy"></span>
                                    <span>مشغول</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-offline"></span>
                                    <span>غير متصل</span>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="mb-3">أوقات الاستجابة:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>منخفضة:</strong> 24-48 ساعة</li>
                                    <li><strong>متوسطة:</strong> 12-24 ساعة</li>
                                    <li><strong>عالية:</strong> 4-8 ساعات</li>
                                    <li><strong>عاجلة:</strong> 1-4 ساعات</li>
                                </ul>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="mb-3">طرق التواصل:</h6>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-phone me-2 text-primary"></i>
                                    <span>************</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-envelope me-2 text-primary"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock me-2 text-primary"></i>
                                    <span>الأحد - الخميس: 8:00 - 16:00</span>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>نصيحة:</strong> قبل إنشاء تذكرة، تحقق من <a href="faq.php" class="alert-link">الأسئلة الشائعة</a> فقد تجد إجابة لسؤالك.
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mt-5">
                    <div class="col-12">
                        <h3 class="text-center mb-5">
                            <i class="fas fa-tools me-2"></i>
                            إجراءات سريعة
                        </h3>
                    </div>
                    
                    <div class="col-md-3 mb-4">
                        <div class="support-card text-center">
                            <i class="fas fa-search support-icon"></i>
                            <h4>البحث في المعرفة</h4>
                            <p>ابحث في قاعدة المعرفة عن حلول للمشاكل الشائعة</p>
                            <button class="btn btn-outline-primary" onclick="openKnowledgeBase()">
                                <i class="fas fa-search me-2"></i>
                                البحث
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-4">
                        <div class="support-card text-center">
                            <i class="fas fa-download support-icon"></i>
                            <h4>تحميل الدليل</h4>
                            <p>حمل دليل المستخدم الكامل للمنصة</p>
                            <button class="btn btn-outline-primary" onclick="downloadManual()">
                                <i class="fas fa-download me-2"></i>
                                التحميل
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-4">
                        <div class="support-card text-center">
                            <i class="fas fa-comments support-icon"></i>
                            <h4>الدردشة المباشرة</h4>
                            <p>تواصل مباشر مع فريق الدعم الفني</p>
                            <button class="btn btn-outline-primary" onclick="startChat()">
                                <i class="fas fa-comment me-2"></i>
                                بدء الدردشة
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-4">
                        <div class="support-card text-center">
                            <i class="fas fa-phone support-icon"></i>
                            <h4>اتصال هاتفي</h4>
                            <p>اتصل بنا مباشرة للحصول على مساعدة فورية</p>
                            <a href="tel:0251234567" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>
                                الاتصال
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 منصة التحويلات بين الإقامات الجامعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updatePriorityBadge() {
            const priority = document.getElementById('priority').value;
            const badge = document.getElementById('priority-badge');
            const text = document.getElementById('priority-text');
            
            if (priority) {
                badge.style.display = 'inline-block';
                badge.className = 'priority-badge priority-' + priority;
                
                const priorityTexts = {
                    'low': 'منخفضة',
                    'medium': 'متوسطة',
                    'high': 'عالية',
                    'urgent': 'عاجلة'
                };
                
                text.textContent = priorityTexts[priority];
            } else {
                badge.style.display = 'none';
            }
        }
        
        function openKnowledgeBase() {
            alert('سيتم تفعيل قاعدة المعرفة قريباً. يمكنك البحث في الأسئلة الشائعة أو إنشاء تذكرة دعم.');
        }
        
        function downloadManual() {
            alert('سيتم إضافة دليل المستخدم للتحميل قريباً. يمكنك التواصل معنا للحصول على المساعدة.');
        }
        
        function startChat() {
            alert('سيتم تفعيل الدردشة المباشرة قريباً. يمكنك إنشاء تذكرة دعم أو الاتصال بنا مباشرة.');
        }
        
        // تحديث شارة الأولوية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updatePriorityBadge();
        });
        
        // إعادة تعيين النموذج عند النجاح
        <?php if ($message_type === "success"): ?>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.querySelector('form').reset();
                updatePriorityBadge();
            }, 3000);
        });
        <?php endif; ?>
    </script>
</body>
</html> 