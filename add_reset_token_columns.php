<?php
// هذا الملف يضيف أعمدة إعادة تعيين كلمة المرور إلى جدول residence_managers

// بدء الجلسة للأمان
session_start();

// فحص الأمان - السماح فقط في بيئة التطوير أو للمسؤولين
$allowed = false;

// فحص إذا كان المستخدم مسؤول
if (isset($_SESSION['admin']) && $_SESSION['admin']['role'] == 'manager') {
    $allowed = true;
}

// فحص إذا كان طلب من localhost (بيئة التطوير)
if (in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1'])) {
    $allowed = true;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة أعمدة إعادة تعيين كلمة المرور</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <div class="container py-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">إضافة أعمدة إعادة تعيين كلمة المرور</h3>
            </div>
            <div class="card-body">
            <?php
            if ($allowed) {
                // تضمين اتصال قاعدة البيانات
                require_once 'db_connect.php';

                echo '<h4>جاري إضافة أعمدة إعادة تعيين كلمة المرور...</h4>';
                
                // استعلام SQL لإضافة الأعمدة
                $sql = "ALTER TABLE `residence_managers` 
                        ADD COLUMN IF NOT EXISTS `reset_token` VARCHAR(255) DEFAULT NULL COMMENT 'رمز إعادة تعيين كلمة المرور',
                        ADD COLUMN IF NOT EXISTS `reset_token_expiry` BIGINT DEFAULT NULL COMMENT 'وقت انتهاء صلاحية رمز إعادة التعيين'";
                
                // تنفيذ الاستعلام
                if ($conn->query($sql) === TRUE) {
                    echo '<div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم إضافة أعمدة إعادة تعيين كلمة المرور بنجاح!
                    </div>';
                    echo '<p>يمكنك الآن <a href="admin_login.php" class="btn btn-primary btn-sm">استخدام ميزة نسيان كلمة المرور</a> بدون مشاكل.</p>';
                } else {
                    echo '<div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في إضافة الأعمدة: ' . $conn->error . '
                    </div>';
                }
                
                // إغلاق الاتصال
                $conn->close();
            } else {
                echo '<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    غير مسموح بالوصول. يجب أن تكون مسؤولاً للقيام بهذه العملية.
                </div>';
            }
            ?>
            </div>
            <div class="card-footer">
                <a href="index.html" class="btn btn-secondary">العودة إلى الصفحة الرئيسية</a>
            </div>
        </div>
    </div>
</body>
</html> 