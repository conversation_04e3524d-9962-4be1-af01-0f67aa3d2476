<?php
session_start(); // بدء الجلسة

// التحقق من تسجيل دخول الطالب
if (!isset($_SESSION['student'])) {
    header("Location: student_login.php"); // توجيه إلى صفحة تسجيل الدخول إذا لم يكن مسجلاً الدخول
    exit();
}

// الاتصال بقاعدة البيانات
include 'db_connect.php';

$message = ""; // متغير لتخزين الرسالة
$message_type = ""; // متغير لتخزين نوع الرسالة

// استرجاع معلومات الطالب الحالي
$student_id = $_SESSION['student']['id'];
$student_query = "SELECT * FROM students WHERE id = $student_id";
$student_result = $conn->query($student_query);
$student_data = $student_result->fetch_assoc();
$current_residence = $student_data['residence'];

// تحديد جنس الطالب من خلال الإقامة الحالية
$current_residence_query = "SELECT gender FROM residences WHERE name = '$current_residence'";
$current_residence_result = $conn->query($current_residence_query);
$student_gender = "";
if ($current_residence_result && $current_residence_result->num_rows > 0) {
    $student_gender = $current_residence_result->fetch_assoc()['gender'];
}

// التحقق من عدم وجود طلب سابق للطالب
$check_request_query = "SELECT * FROM requests WHERE student_id = $student_id AND status = 'pending'";
$check_request_result = $conn->query($check_request_query);
$has_pending_request = ($check_request_result && $check_request_result->num_rows > 0);

// استرجاع قائمة الإقامات المتاحة (باستثناء الإقامة الحالية للطالب ومطابقة للجنس)
$residences_query = "SELECT * FROM residences WHERE name != '$current_residence' AND gender = '$student_gender' ORDER BY name";
$residences_result = $conn->query($residences_query);

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $student_id = $_SESSION['student']['id']; // الحصول على ID الطالب
    $residence_requested = $conn->real_escape_string($_POST['residence_requested']); // الحصول على الإقامة المطلوبة

    // التحقق من عدم وجود طلب سابق
    if ($has_pending_request) {
        $message = "لا يمكن تقديم أكثر من طلب واحد في نفس الوقت";
        $message_type = "danger";
    } else {
        // التحقق من توافق الجنس مع الإقامة المطلوبة
        $check_gender_query = "SELECT gender FROM residences WHERE name = '$residence_requested'";
        $check_gender_result = $conn->query($check_gender_query);
        
        if ($check_gender_result && $check_gender_result->num_rows > 0) {
            $residence_gender = $check_gender_result->fetch_assoc()['gender'];
            
            if ($residence_gender == $student_gender) {
                // إدخال الطلب في قاعدة البيانات
                $sql = "INSERT INTO requests (student_id, residence_requested, status, release_status) VALUES ($student_id, '$residence_requested', 'pending', 'no')";
                
                if ($conn->query($sql) === TRUE) {
                    $message = "تم تقديم طلب التحويل بنجاح!";
                    $message_type = "success";
                    // إعادة تحميل الصفحة لتحديث حالة الطلبات
                    header("Location: submit_request.php?success=1");
                    exit();
                } else {
                    $message = "خطأ: " . $conn->error;
                    $message_type = "danger";
                }
            } else {
                $message = "لا يمكن اختيار إقامة لا تتوافق مع جنسك";
                $message_type = "danger";
            }
        } else {
            $message = "الإقامة المطلوبة غير موجودة";
            $message_type = "danger";
        }
    }
}

// عرض رسالة النجاح بعد إعادة التوجيه
if (isset($_GET['success']) && $_GET['success'] == 1) {
    $message = "تم تقديم طلب التحويل بنجاح!";
    $message_type = "success";
    $has_pending_request = true; // تحديث حالة الطلب بعد النجاح
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقديم طلب التحويل | منصة التحويلات بين الإقامات الجامعية</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .main-content {
            flex: 1;
            padding: 2rem 0;
        }
        .request-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .student-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-right: 4px solid #0d6efd;
        }
        .student-info h5 {
            color: #0d6efd;
            margin-bottom: 1rem;
        }
        .student-info .info-item {
            margin-bottom: 0.5rem;
        }
        .student-info .info-label {
            font-weight: bold;
            color: #495057;
        }
        .pending-request-card {
            background-color: #fff3cd;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-right: 4px solid #ffc107;
        }
        .pending-request-card h5 {
            color: #856404;
            margin-bottom: 1rem;
        }
        .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
        .btn-submit {
            padding: 0.6rem 2rem;
            font-size: 1.1rem;
        }
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .navbar-nav .nav-link.active {
            font-weight: bold;
            color: #0d6efd;
        }
        .student-badge {
            background-color: #e9ecef;
            color: #495057;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- القائمة العلوية -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-exchange-alt text-primary me-2"></i>
                منصة التحويلات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="submit_request.php">
                            <i class="fas fa-plus-circle me-1"></i>
                            تقديم طلب جديد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="view_requests.php">
                            <i class="fas fa-list-alt me-1"></i>
                            عرض طلباتي
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="student-badge me-3">
                        <i class="fas fa-user-graduate"></i>
                        <?php echo $student_data['first_name'] . ' ' . $student_data['last_name']; ?>
                    </span>
                    <a href="logout.php" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">
                        <i class="fas fa-exchange-alt text-primary me-2"></i>
                        طلب تحويل الإقامة
                    </h2>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- معلومات الطالب -->
                <div class="student-info">
                    <h5>
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الطالب
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">الاسم الكامل:</span>
                                <?php echo $student_data['first_name'] . ' ' . $student_data['last_name']; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">رقم البكالوريا:</span>
                                <?php echo $student_data['baccalaureate_number']; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">التخصص:</span>
                                <?php echo $student_data['specialization']; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">الإقامة الحالية:</span>
                                <span class="badge bg-primary"><?php echo $current_residence; ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($has_pending_request): ?>
                    <!-- رسالة وجود طلب قيد المعالجة -->
                    <div class="pending-request-card">
                        <h5>
                            <i class="fas fa-exclamation-circle me-2"></i>
                            لديك طلب تحويل قيد المعالجة
                        </h5>
                        <p class="mb-3">لا يمكن تقديم طلب جديد حتى يتم البت في الطلب الحالي.</p>
                        <a href="view_requests.php" class="btn btn-warning">
                            <i class="fas fa-eye me-1"></i>
                            عرض حالة طلبك
                        </a>
                    </div>
                <?php else: ?>
                    <!-- نموذج تقديم الطلب -->
                    <div class="request-card">
                        <h5 class="mb-4">
                            <i class="fas fa-paper-plane me-2"></i>
                            تقديم طلب جديد
                        </h5>
                        <form action="submit_request.php" method="POST">
                            <div class="mb-4">
                                <label for="residence_requested" class="form-label fw-bold">الإقامة المطلوبة:</label>
                                <select class="form-select form-select-lg" id="residence_requested" name="residence_requested" required>
                                    <option value="">-- اختر الإقامة المطلوبة --</option>
                                    <?php 
                                    if ($residences_result && $residences_result->num_rows > 0) {
                                        while($residence = $residences_result->fetch_assoc()) {
                                            echo '<option value="' . $residence['name'] . '">' . $residence['name'] . ' (' . $residence['gender'] . ')</option>';
                                        }
                                    } else {
                                        echo '<option value="" disabled>لا توجد إقامات متاحة للتحويل</option>';
                                    }
                                    ?>
                                </select>
                                <div class="form-text text-muted mt-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يتم عرض الإقامات المتوافقة مع جنسك فقط
                                </div>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" class="btn btn-primary btn-submit">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    تقديم الطلب
                                </button>
                            </div>
                        </form>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- الفوتر -->
    <footer class="bg-dark text-white text-center py-3 mt-auto">
        <div class="container">
            <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
        </div>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
