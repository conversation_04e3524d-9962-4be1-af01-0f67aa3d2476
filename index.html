<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة التحويلات بين الإقامات الجامعية</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- إضافة Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Styles */
        .top-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .top-header h1 {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,100 1000,0 1000,100"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        /* Main Content */
        .main-content {
            background: white;
            position: relative;
            z-index: 1;
        }

        .content-wrapper {
            padding: 80px 0;
        }

        /* Login Cards */
        .login-section {
            margin-bottom: 80px;
        }

        .login-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            height: 100%;
            position: relative;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--gradient-primary);
        }

        .login-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .login-card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px 20px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .login-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-card-body {
            padding: 40px 30px;
            text-align: center;
        }

        .login-description {
            color: #6c757d;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .btn-login {
            padding: 15px 40px;
            font-weight: 600;
            border-radius: 50px;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-student {
            background: var(--gradient-primary);
            border: none;
        }

        .btn-admin {
            background: var(--gradient-secondary);
            border: none;
        }

        /* Features Section */
        .features-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 60px;
            color: var(--dark-color);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-success);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 25px;
            background: var(--gradient-success);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        .feature-description {
            color: #6c757d;
            line-height: 1.6;
            font-size: 1rem;
        }

        /* Stats Section */
        .stats-section {
            padding: 80px 0;
            background: var(--gradient-primary);
            color: white;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Footer */
        .site-footer {
            background: var(--dark-color);
            color: white;
            padding: 40px 0 20px;
        }

        .footer-content {
            text-align: center;
        }

        .footer-links {
            margin-bottom: 20px;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            margin: 0 15px;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: white;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .login-card {
                margin-bottom: 30px;
            }

            .feature-card {
                margin-bottom: 30px;
            }
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .floating-1 {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-2 {
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-3 {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="top-header">
        <div class="container">
            <div class="d-flex justify-content-center align-items-center">
                <h1>
                    <i class="fas fa-university me-3"></i>
                    مديرية الخدمات الجامعية المسيلة-وسط
                </h1>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <header class="hero-section">
        <div class="floating-element floating-1">
            <i class="fas fa-graduation-cap fa-3x"></i>
        </div>
        <div class="floating-element floating-2">
            <i class="fas fa-building fa-3x"></i>
        </div>
        <div class="floating-element floating-3">
            <i class="fas fa-users fa-3x"></i>
        </div>
        
        <div class="container text-center hero-content">
            <h1 class="hero-title animate-fade-in-up">منصة التحويلات بين الإقامات الجامعية</h1>
            <p class="hero-subtitle animate-fade-in-up">نظام متكامل لإدارة طلبات التحويل بين الإقامات الجامعية بطريقة سهلة وفعالة وآمنة</p>
            <div class="animate-fade-in-up">
                <a href="#login-section" class="btn btn-light btn-lg px-5 py-3">
                    <i class="fas fa-arrow-down me-2"></i>
                    ابدأ الآن
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <!-- Login Section -->
                <section id="login-section" class="login-section">
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="login-card">
                                <div class="login-card-header">
                                    <i class="fas fa-user-graduate login-icon"></i>
                                    <h2 class="h3">دخول الطلبة</h2>
                                </div>
                                <div class="login-card-body">
                                    <p class="login-description">تسجيل الدخول باستخدام رقم البكالوريا لتقديم طلبات التحويل بين الإقامات الجامعية ومتابعة حالة طلباتك</p>
                                    <a href="student_login.php" class="btn btn-login btn-student">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        دخول الطلبة
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mb-4">
                            <div class="login-card">
                                <div class="login-card-header">
                                    <i class="fas fa-user-shield login-icon"></i>
                                    <h2 class="h3">دخول المسؤولين</h2>
                                </div>
                                <div class="login-card-body">
                                    <p class="login-description">تسجيل الدخول للمسؤولين والمكلفين بالإقامات لإدارة طلبات التحويل والمراجعة</p>
                                    <a href="admin_login.php" class="btn btn-login btn-admin">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        دخول المسؤولين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Features Section -->
                <section class="features-section">
                    <h2 class="section-title">مميزات المنصة</h2>
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="feature-card">
                                <i class="fas fa-tasks feature-icon"></i>
                                <h3 class="feature-title">إدارة الطلبات</h3>
                                <p class="feature-description">إمكانية تقديم ومتابعة طلبات التحويل بين الإقامات الجامعية بكل سهولة وشفافية</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="feature-card">
                                <i class="fas fa-exchange-alt feature-icon"></i>
                                <h3 class="feature-title">عملية التحويل</h3>
                                <p class="feature-description">تسهيل عملية التحويل بين الإقامات وتوفير الوقت والجهد للطلبة والإدارة</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="feature-card">
                                <i class="fas fa-chart-line feature-icon"></i>
                                <h3 class="feature-title">متابعة الإحصائيات</h3>
                                <p class="feature-description">توفير إحصائيات دقيقة عن طلبات التحويل والإقامات الجامعية</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="feature-card">
                                <i class="fas fa-shield-alt feature-icon"></i>
                                <h3 class="feature-title">الأمان والحماية</h3>
                                <p class="feature-description">نظام آمن ومحمي لحماية بيانات الطلبة وضمان سرية المعلومات</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="feature-card">
                                <i class="fas fa-mobile-alt feature-icon"></i>
                                <h3 class="feature-title">تصميم متجاوب</h3>
                                <p class="feature-description">واجهة متجاوبة تعمل على جميع الأجهزة والهواتف الذكية</p>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="feature-card">
                                <i class="fas fa-clock feature-icon"></i>
                                <h3 class="feature-title">خدمة 24/7</h3>
                                <p class="feature-description">خدمة متاحة على مدار الساعة لتقديم طلبات التحويل في أي وقت</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Stats Section -->
                <section class="stats-section">
                    <div class="container">
                        <div class="row">
                            <div class="col-md-3 col-6 mb-4">
                                <div class="stat-item">
                                    <div class="stat-number">1000+</div>
                                    <div class="stat-label">طالب مسجل</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-4">
                                <div class="stat-item">
                                    <div class="stat-number">500+</div>
                                    <div class="stat-label">طلب تحويل</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-4">
                                <div class="stat-item">
                                    <div class="stat-number">10</div>
                                    <div class="stat-label">إقامة جامعية</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-4">
                                <div class="stat-item">
                                    <div class="stat-number">99%</div>
                                    <div class="stat-label">معدل الرضا</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="about.php"><i class="fas fa-info-circle me-1"></i>حول المنصة</a>
                    <a href="faq.php"><i class="fas fa-question-circle me-1"></i>الأسئلة الشائعة</a>
                    <a href="contact.php"><i class="fas fa-phone me-1"></i>اتصل بنا</a>
                    <a href="support.php"><i class="fas fa-headset me-1"></i>الدعم الفني</a>
                </div>
                <p class="mb-0">&copy; 2025 منصة التحويلات بين الإقامات الجامعية - جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.top-header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.backdropFilter = 'blur(20px)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.1)';
                header.style.backdropFilter = 'blur(10px)';
            }
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease-out';
            observer.observe(card);
        });
    </script>
</body>
</html>

