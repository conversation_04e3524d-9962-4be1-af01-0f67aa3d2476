/* أنماط عامة للموقع */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* استيراد خط القاهرة من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

body {
    font-family: 'Cairo', Arial, sans-serif;
    direction: rtl;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* باقي الأنماط تبقى كما هي */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: 1rem;
}

/* تخصيص الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #0a58ca;
    text-decoration: underline;
}

/* تخصيص الأزرار */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0a58ca;
    border-color: #0a58ca;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #157347;
    border-color: #157347;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #bb2d3b;
}

/* تخصيص القوائم */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.8rem 1rem;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 5px;
    margin: 0 0.2rem;
}

.nav-link:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.nav-link.active {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary-color) !important;
    font-weight: 700;
}

/* تخصيص البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* تخصيص الجداول */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 700;
    color: #495057;
    background-color: #f8f9fa;
    border-top: none;
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* تخصيص النماذج */
.form-control, .form-select {
    padding: 0.6rem 1rem;
    border-radius: 5px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
}

/* تخصيص الشارات */
.badge {
    padding: 0.5rem 0.8rem;
    font-weight: 500;
    border-radius: 50px;
}

/* تخصيص التنبيهات */
.alert {
    border-radius: 10px;
    padding: 1rem 1.5rem;
    border: none;
    margin-bottom: 1.5rem;
}

/* تخصيص الفوتر */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 1.5rem 0;
    margin-top: auto;
}

/* تخصيص صفحة تسجيل الدخول */
.login-container {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* تخصيص صفحة الطلبات */
.student-info {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-right: 4px solid var(--primary-color);
}

.info-item {
    margin-bottom: 0.5rem;
}

.info-label {
    font-weight: 700;
    color: #495057;
    margin-left: 0.5rem;
}

/* تخصيص للشاشات الصغيرة */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table th, .table td {
        padding: 0.5rem;
    }
    
    .btn {
        padding: 0.4rem 1rem;
    }
}

/* تخصيص للطباعة */
@media print {
    body {
        background-color: white;
    }
    
    .navbar, .btn-back, footer, .no-print {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .table th {
        background-color: #f1f1f1 !important;
        color: black !important;
    }
}

/* تخصيص لوضع الظلام (إذا تم تفعيله مستقبلاً) */
[data-bs-theme="dark"] {
    --bs-body-bg: #212529;
    --bs-body-color: #f8f9fa;
}

[data-bs-theme="dark"] .card,
[data-bs-theme="dark"] .navbar {
    background-color: #2c3034;
}

[data-bs-theme="dark"] .table th {
    background-color: #2c3034;
    color: #f8f9fa;
}

[data-bs-theme="dark"] .student-info {
    background-color: #2c3034;
}

/* تخصيص الإجراءات السريعة */
.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.quick-actions a {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 15px 10px;
    border-radius: 10px;
    background-color: #f8f9fa;
    color: var(--dark-color);
    transition: all 0.3s ease;
    text-decoration: none;
    width: 100%;
    height: 100%;
}

.quick-actions a:hover {
    transform: translateY(-5px);
    background-color: #e9ecef;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-decoration: none;
}

.quick-actions i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.quick-actions span {
    font-weight: 500;
}

/* تخصيص شارات الحالة */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.6rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-accepted {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.release-yes {
    background-color: #d4edda;
    color: #155724;
}

.release-no {
    background-color: #e2e3e5;
    color: #383d41;
}

