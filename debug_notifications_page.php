<?php
// Iniciar el almacenamiento en búfer de salida
ob_start();

// Iniciar una sesión simulada
session_start();
$_SESSION['admin'] = ['id' => 23, 'role' => 'manager'];

// Definir constantes necesarias si no están definidas
if (!defined('SITE_NAME')) {
    define('SITE_NAME', 'منصة التحويلات بين الإقامات الجامعية');
}

// Redirigir la salida a un búfer
include 'view_notifications.php';

// Capturar la salida
$output = ob_get_contents();
ob_end_clean();

// Comprobar si hay errores
$error_pattern = "/(Warning|Notice|Parse error|Fatal error|Error)/i";
if (preg_match($error_pattern, $output, $matches)) {
    echo "Se encontraron errores en la página de notificaciones:\n";
    echo preg_replace('/<br\s*\/?>/i', "\n", $output);
} else {
    echo "La página de notificaciones se cargó sin errores de PHP.\n";
    
    // Verificar si hay contenido
    if (strlen($output) > 1000) {
        echo "La página tiene contenido (primeros 100 caracteres):\n";
        echo substr($output, 0, 100) . "...\n";
    } else {
        echo "La página parece estar vacía o tiene muy poco contenido.\n";
    }
}
?> 