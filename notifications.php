<?php
/**
 * notifications.php
 * ملف إدارة الإشعارات للطلبة والمسؤولين
 */

/**
 * إضافة إشعار جديد إلى قاعدة البيانات
 * 
 * @param int $user_id معرف المستخدم
 * @param string $user_type نوع المستخدم ('admin' أو 'student')
 * @param string $title عنوان الإشعار
 * @param string $message نص الإشعار
 * @param string $type نوع الإشعار ('info', 'success', 'warning', 'danger')
 * @param string $link رابط الإشعار (اختياري)
 * @return int|false معرف الإشعار الجديد أو false في حالة الفشل
 */
function add_notification($user_id, $user_type, $title, $message, $type = 'info', $link = null) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO notifications 
            (user_id, user_type, title, message, type, link, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->bind_param("isssss", $user_id, $user_type, $title, $message, $type, $link);
        $result = $stmt->execute();
        
        if ($result) {
            return $conn->insert_id;
        } else {
            return false;
        }
    } catch (Exception $e) {
        log_message("خطأ في إضافة إشعار: " . $e->getMessage(), "error");
        return false;
    }
}

/**
 * إضافة إشعار لجميع الطلبة
 * 
 * @param string $title عنوان الإشعار
 * @param string $message نص الإشعار
 * @param string $type نوع الإشعار
 * @param string $link رابط الإشعار (اختياري)
 * @return bool نجاح أو فشل العملية
 */
function add_notification_to_all_students($title, $message, $type = 'info', $link = null) {
    global $conn;
    
    try {
        // الحصول على قائمة جميع الطلبة
        $students_result = $conn->query("SELECT id FROM students");
        
        if ($students_result->num_rows > 0) {
            $conn->begin_transaction();
            
            while ($student = $students_result->fetch_assoc()) {
                $stmt = $conn->prepare("
                    INSERT INTO notifications 
                    (user_id, user_type, title, message, type, link, created_at) 
                    VALUES (?, 'student', ?, ?, ?, ?, NOW())
                ");
                
                $stmt->bind_param("issss", $student['id'], $title, $message, $type, $link);
                $stmt->execute();
            }
            
            $conn->commit();
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollback();
        }
        log_message("خطأ في إضافة إشعار لجميع الطلبة: " . $e->getMessage(), "error");
        return false;
    }
}

/**
 * إضافة إشعار لجميع المسؤولين
 * 
 * @param string $title عنوان الإشعار
 * @param string $message نص الإشعار
 * @param string $type نوع الإشعار
 * @param string $link رابط الإشعار (اختياري)
 * @return bool نجاح أو فشل العملية
 */
function add_notification_to_all_admins($title, $message, $type = 'info', $link = null) {
    global $conn;
    
    try {
        // الحصول على قائمة جميع المسؤولين
        $admins_result = $conn->query("SELECT id FROM residence_managers");
        
        if ($admins_result->num_rows > 0) {
            $conn->begin_transaction();
            
            while ($admin = $admins_result->fetch_assoc()) {
                $stmt = $conn->prepare("
                    INSERT INTO notifications 
                    (user_id, user_type, title, message, type, link, created_at) 
                    VALUES (?, 'admin', ?, ?, ?, ?, NOW())
                ");
                
                $stmt->bind_param("issss", $admin['id'], $title, $message, $type, $link);
                $stmt->execute();
            }
            
            $conn->commit();
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollback();
        }
        log_message("خطأ في إضافة إشعار لجميع المسؤولين: " . $e->getMessage(), "error");
        return false;
    }
}

/**
 * الحصول على عدد الإشعارات غير المقروءة للمستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @param string $user_type نوع المستخدم ('admin' أو 'student')
 * @return int عدد الإشعارات غير المقروءة
 */
function get_unread_notifications_count($user_id, $user_type) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM notifications 
        WHERE user_id = ? AND user_type = ? AND is_read = 0
    ");
    
    $stmt->bind_param("is", $user_id, $user_type);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    return $row['count'];
}

/**
 * جلب الإشعارات للمستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @param string $user_type نوع المستخدم ('admin' أو 'student')
 * @param int $limit عدد الإشعارات المطلوبة
 * @param int $offset موقع البداية
 * @param bool $unread_only جلب الإشعارات غير المقروءة فقط
 * @return array مصفوفة من الإشعارات
 */
function get_user_notifications($user_id, $user_type, $limit = 10, $offset = 0, $unread_only = false) {
    global $conn;
    
    $sql = "
        SELECT * FROM notifications 
        WHERE user_id = ? AND user_type = ?
    ";
    
    if ($unread_only) {
        $sql .= " AND is_read = 0";
    }
    
    $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isii", $user_id, $user_type, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $notifications = [];
    while ($row = $result->fetch_assoc()) {
        $notifications[] = $row;
    }
    
    return $notifications;
}

/**
 * تحديث حالة قراءة الإشعار
 * 
 * @param int $notification_id معرف الإشعار
 * @param bool $is_read حالة القراءة
 * @return bool نجاح أو فشل العملية
 */
function mark_notification_as_read($notification_id, $is_read = true) {
    global $conn;
    
    $stmt = $conn->prepare("UPDATE notifications SET is_read = ?, read_at = NOW() WHERE id = ?");
    $read_value = $is_read ? 1 : 0;
    $stmt->bind_param("ii", $read_value, $notification_id);
    
    return $stmt->execute();
}

/**
 * تحديث حالة قراءة جميع إشعارات المستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @param string $user_type نوع المستخدم ('admin' أو 'student')
 * @return bool نجاح أو فشل العملية
 */
function mark_all_notifications_as_read($user_id, $user_type) {
    global $conn;
    
    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1, read_at = NOW() WHERE user_id = ? AND user_type = ? AND is_read = 0");
    $stmt->bind_param("is", $user_id, $user_type);
    
    return $stmt->execute();
}

/**
 * حذف إشعار
 * 
 * @param int $notification_id معرف الإشعار
 * @return bool نجاح أو فشل العملية
 */
function delete_notification($notification_id) {
    global $conn;
    
    $stmt = $conn->prepare("DELETE FROM notifications WHERE id = ?");
    $stmt->bind_param("i", $notification_id);
    
    return $stmt->execute();
}

/**
 * إرسال إشعار عند إنشاء طلب جديد
 * 
 * @param int $request_id معرف الطلب
 * @return bool نجاح أو فشل العملية
 */
function notify_new_request($request_id) {
    global $conn;
    
    try {
        // الحصول على معلومات الطلب والطالب
        $stmt = $conn->prepare("
            SELECT r.*, s.first_name, s.last_name 
            FROM requests r
            JOIN students s ON r.student_id = s.id
            WHERE r.id = ?
        ");
        $stmt->bind_param("i", $request_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $request_data = $result->fetch_assoc();
            $student_id = $request_data['student_id'];
            $student_name = $request_data['first_name'] . ' ' . $request_data['last_name'];
            $residence_requested = $request_data['residence_requested'];
            
            // إشعار للطالب
            add_notification(
                $student_id,
                'student',
                'تم تقديم طلب تحويل جديد',
                "تم تقديم طلب تحويل إلى إقامة $residence_requested بنجاح. ستتم مراجعة طلبك قريباً.",
                'success',
                'view_requests.php'
            );
            
            // إشعار للمسؤولين عن الإقامة المطلوبة
            $admin_stmt = $conn->prepare("
                SELECT id FROM residence_managers 
                WHERE residence_name = ? OR role = 'manager'
            ");
            $admin_stmt->bind_param("s", $residence_requested);
            $admin_stmt->execute();
            $admin_result = $admin_stmt->get_result();
            
            while ($admin = $admin_result->fetch_assoc()) {
                add_notification(
                    $admin['id'],
                    'admin',
                    'طلب تحويل جديد',
                    "تم تقديم طلب تحويل جديد من الطالب $student_name إلى إقامة $residence_requested.",
                    'info',
                    'manage_requests.php'
                );
            }
            
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        log_message("خطأ في إرسال إشعارات الطلب الجديد: " . $e->getMessage(), "error");
        return false;
    }
}

/**
 * إرسال إشعار عند تغيير حالة الطلب
 * 
 * @param int $request_id معرف الطلب
 * @param string $old_status الحالة القديمة
 * @param string $new_status الحالة الجديدة
 * @return bool نجاح أو فشل العملية
 */
function notify_request_status_change($request_id, $old_status, $new_status) {
    global $conn;
    
    try {
        // الحصول على معلومات الطلب والطالب
        $stmt = $conn->prepare("
            SELECT r.*, s.first_name, s.last_name 
            FROM requests r
            JOIN students s ON r.student_id = s.id
            WHERE r.id = ?
        ");
        $stmt->bind_param("i", $request_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $request_data = $result->fetch_assoc();
            $student_id = $request_data['student_id'];
            $student_name = $request_data['first_name'] . ' ' . $request_data['last_name'];
            $residence_requested = $request_data['residence_requested'];
            
            // ترجمة الحالة
            $new_status_ar = ($new_status == 'pending') ? 'قيد الدراسة' : (($new_status == 'accepted') ? 'مقبول' : 'مرفوض');
            
            // تحديد نوع الإشعار
            $notification_type = ($new_status == 'accepted') ? 'success' : (($new_status == 'rejected') ? 'danger' : 'info');
            
            // إشعار للطالب
            add_notification(
                $student_id,
                'student',
                'تغيير حالة طلب التحويل',
                "تم تغيير حالة طلب التحويل الخاص بك إلى $new_status_ar.",
                $notification_type,
                'view_requests.php'
            );
            
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        log_message("خطأ في إرسال إشعارات تغيير حالة الطلب: " . $e->getMessage(), "error");
        return false;
    }
}
?> 