<?php
include 'db_connect.php';

echo "<h2>فحص حالة كلمات المرور</h2>";

// استرجاع جميع المستخدمين
$sql = "SELECT id, username, password FROM residence_managers";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>المعرف</th><th>اسم المستخدم</th><th>نوع كلمة المرور</th><th>كلمة المرور</th></tr>";
    
    while($row = $result->fetch_assoc()) {
        $id = $row['id'];
        $username = $row['username'];
        $password = $row['password'];
        
        // التحقق مما إذا كانت كلمة المرور مشفرة
        $is_hashed = (strlen($password) > 40 && strpos($password, '$2y$') === 0);
        
        echo "<tr>";
        echo "<td>$id</td>";
        echo "<td>$username</td>";
        echo "<td>" . ($is_hashed ? "مشفرة" : "غير مشفرة") . "</td>";
        echo "<td>" . substr($password, 0, 20) . "...</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>إعادة تشفير كلمات المرور</h3>";
    echo "<p>إذا كانت كلمات المرور غير مشفرة، يمكنك <a href='update_passwords.php'>النقر هنا</a> لإعادة تشفيرها.</p>";
    
    echo "<h3>إعادة تعيين كلمات المرور</h3>";
    echo "<p>إذا كنت ترغب في إعادة تعيين كلمات المرور إلى القيم الافتراضية، يمكنك استخدام النموذج أدناه:</p>";
    
    echo "<form method='post' action='reset_password.php'>";
    echo "<label for='username'>اسم المستخدم:</label>";
    echo "<select name='username' id='username'>";
    
    // إعادة تعيين مؤشر النتائج
    $result->data_seek(0);
    while($row = $result->fetch_assoc()) {
        echo "<option value='" . $row['username'] . "'>" . $row['username'] . "</option>";
    }
    
    echo "</select>";
    echo "<br><br>";
    echo "<label for='new_password'>كلمة المرور الجديدة:</label>";
    echo "<input type='text' name='new_password' id='new_password' required>";
    echo "<br><br>";
    echo "<input type='submit' value='إعادة تعيين كلمة المرور'>";
    echo "</form>";
    
} else {
    echo "لم يتم العثور على أي مستخدمين.";
}

$conn->close();
?>