<?php
session_start();
include 'db_connect.php';

// تهيئة المصفوفة للرد
$response = [
    'success' => false,
    'message' => ''
];

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    $response['message'] = 'غير مصرح لك بهذه العملية';
    echo json_encode($response);
    exit();
}

// التحقق من أن الطلب بطريقة POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'طريقة طلب غير صالحة';
    echo json_encode($response);
    exit();
}

// التحقق من رمز CSRF
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    $response['message'] = 'فشل التحقق من الأمان';
    echo json_encode($response);
    exit();
}

// التحقق من وجود معرف الطلب والحالة
if (!isset($_POST['request_id']) || !isset($_POST['status'])) {
    $response['message'] = 'بيانات غير مكتملة';
    echo json_encode($response);
    exit();
}

$request_id = intval($_POST['request_id']);
$status = $_POST['status'];

// التحقق من صحة قيمة الحالة
$valid_statuses = ['pending', 'accepted', 'rejected'];
if (!in_array($status, $valid_statuses)) {
    $response['message'] = 'قيمة الحالة غير صالحة';
    echo json_encode($response);
    exit();
}

// إرسال الرد بصيغة JSON
header('Content-Type: application/json');

try {
    // الحصول على الحالة القديمة
    $get_old_status = $conn->prepare("SELECT status FROM requests WHERE id = ?");
    $get_old_status->bind_param("i", $request_id);
    $get_old_status->execute();
    $old_status_result = $get_old_status->get_result();
    $old_status_row = $old_status_result->fetch_assoc();
    $old_status = $old_status_row['status'];
    
    // تحديث حالة الطلب في قاعدة البيانات
    $stmt = $conn->prepare("UPDATE requests SET status = ? WHERE id = ?");
    $stmt->bind_param("si", $status, $request_id);
    $result = $stmt->execute();
    
    if ($result) {
        // إرسال إشعار بتغيير حالة الطلب
        include_once 'notifications.php';
        notify_request_status_change($request_id, $old_status, $status);
        
        // تسجيل العملية في سجل الأنشطة
        $admin_id = $_SESSION['admin']['id'];
        $log_stmt = $conn->prepare("
            INSERT INTO activity_log (user_id, user_type, action, details) 
            VALUES (?, 'admin', 'تغيير حالة طلب', ?)
        ");
        $details = "تغيير حالة الطلب رقم $request_id من " . 
                   ($old_status == 'pending' ? 'قيد الدراسة' : ($old_status == 'accepted' ? 'مقبول' : 'مرفوض')) . 
                   " إلى " . 
                   ($status == 'pending' ? 'قيد الدراسة' : ($status == 'accepted' ? 'مقبول' : 'مرفوض'));
        $log_stmt->bind_param("is", $admin_id, $details);
        $log_stmt->execute();
        
        $response['success'] = true;
        $response['message'] = 'تم تحديث حالة الطلب بنجاح';
    } else {
        $response['message'] = 'فشل تحديث حالة الطلب';
    }
} catch (Exception $e) {
    $response['message'] = 'حدث خطأ أثناء تحديث الحالة: ' . $e->getMessage();
}

echo json_encode($response);
?> 