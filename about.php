<?php
session_start();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حول المنصة | منصة التحويلات بين الإقامات الجامعية</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .main-content {
            background: white;
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            position: relative;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,100 1000,0 1000,100"/></svg>');
            background-size: cover;
        }

        .content-section {
            padding: 60px 0;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            margin-bottom: 30px;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .timeline {
            position: relative;
            padding: 20px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--primary-color);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 40px;
        }

        .timeline-content {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            width: 45%;
        }

        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: 0;
        }

        .timeline-item:nth-child(even) .timeline-content {
            margin-left: 55%;
        }

        .timeline-dot {
            position: absolute;
            left: 50%;
            top: 20px;
            width: 20px;
            height: 20px;
            background: var(--primary-color);
            border-radius: 50%;
            transform: translateX(-50%);
            z-index: 2;
        }

        .stats-section {
            background: var(--gradient-primary);
            color: white;
            padding: 60px 0;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .timeline::before {
                left: 20px;
            }
            
            .timeline-content {
                width: calc(100% - 50px);
                margin-left: 50px !important;
            }
            
            .timeline-dot {
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h4 mb-0 text-white">
                    <i class="fas fa-university me-2"></i>
                    حول المنصة
                </h1>
                <a href="index.html" class="btn btn-outline-light">
                    <i class="fas fa-home me-1"></i>
                    الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">منصة التحويلات بين الإقامات الجامعية</h1>
            <p class="lead mb-0">نظام متكامل لإدارة طلبات التحويل بين الإقامات الجامعية بطريقة سهلة وفعالة وآمنة</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <!-- About Section -->
        <section class="content-section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center mb-5">
                        <h2 class="display-6 fw-bold mb-4">عن المنصة</h2>
                        <p class="lead text-muted">
                            منصة التحويلات بين الإقامات الجامعية هي نظام إلكتروني متطور تم تطويره لتسهيل عملية التحويل بين الإقامات الجامعية 
                            للطلبة في الجزائر. يوفر النظام واجهة سهلة الاستخدام وآمنة لجميع الأطراف المعنية.
                        </p>
                    </div>
                </div>

                <!-- Features Grid -->
                <div class="row">
                    <div class="col-lg-4 col-md-6">
                        <div class="feature-card">
                            <i class="fas fa-users feature-icon"></i>
                            <h4>للطلبة</h4>
                            <p>تقديم طلبات التحويل بسهولة ومتابعة حالة الطلبات في الوقت الفعلي</p>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="feature-card">
                            <i class="fas fa-user-shield feature-icon"></i>
                            <h4>للمسؤولين</h4>
                            <p>إدارة الطلبات ومراجعتها واتخاذ القرارات المناسبة بكل شفافية</p>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="feature-card">
                            <i class="fas fa-chart-line feature-icon"></i>
                            <h4>للإدارة</h4>
                            <p>متابعة الإحصائيات والتقارير لتحسين الخدمات واتخاذ القرارات المبنية على البيانات</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Timeline Section -->
        <section class="content-section bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="display-6 fw-bold">مراحل التطوير</h2>
                    </div>
                </div>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <h5>المرحلة الأولى</h5>
                            <p>تطوير النظام الأساسي وإعداد قاعدة البيانات</p>
                            <small class="text-muted">يناير 2025</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <h5>المرحلة الثانية</h5>
                            <p>تطوير واجهات المستخدم وإضافة ميزات الأمان</p>
                            <small class="text-muted">فبراير 2025</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <h5>المرحلة الثالثة</h5>
                            <p>اختبار النظام وإصلاح الأخطاء</p>
                            <small class="text-muted">مارس 2025</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <h5>المرحلة الرابعة</h5>
                            <p>إطلاق النظام وتدريب المستخدمين</p>
                            <small class="text-muted">أبريل 2025</small>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats-section">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="display-6 fw-bold">إحصائيات المنصة</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <div class="stat-number">1000+</div>
                            <div class="stat-label">طالب مسجل</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">طلب تحويل</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <div class="stat-number">10</div>
                            <div class="stat-label">إقامة جامعية</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <div class="stat-number">99%</div>
                            <div class="stat-label">معدل الرضا</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technology Section -->
        <section class="content-section">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="display-6 fw-bold">التقنيات المستخدمة</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="feature-card text-center">
                            <i class="fab fa-php feature-icon"></i>
                            <h5>PHP</h5>
                            <p>لغة البرمجة الخلفية</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="feature-card text-center">
                            <i class="fas fa-database feature-icon"></i>
                            <h5>MySQL</h5>
                            <p>قاعدة البيانات</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="feature-card text-center">
                            <i class="fab fa-html5 feature-icon"></i>
                            <h5>HTML5</h5>
                            <p>هيكل الصفحات</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="feature-card text-center">
                            <i class="fab fa-css3-alt feature-icon"></i>
                            <h5>CSS3</h5>
                            <p>التصميم والأنماط</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 منصة التحويلات بين الإقامات الجامعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 