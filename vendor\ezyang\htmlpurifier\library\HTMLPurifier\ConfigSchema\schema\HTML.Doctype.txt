HTML.Doctype
TYPE: string/null
DEFAULT: NULL
--DESCRIPTION--
Doctype to use during filtering. Technically speaking this is not actually
a doctype (as it does not identify a corresponding DTD), but we are using
this name for sake of simplicity. When non-blank, this will override any
older directives like %HTML.XHTML or %HTML.Strict.
--ALLOWED--
'HTML 4.01 Transitional', 'HTML 4.01 Strict', 'XHTML 1.0 Transitional', 'XHTML 1.0 Strict', 'XHTML 1.1'
--# vim: et sw=4 sts=4
