<?php
session_start();

// الاتصال بقاعدة البيانات
include 'db_connect.php';

$message = ""; // متغير لتخزين الرسائل
$message_type = ""; // متغير لتخزين نوع الرسالة

// إنشاء رمز CSRF إذا لم يكن موجودًا
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// استرجاع قائمة الإقامات الجامعية
$residences_query = "SELECT * FROM residences ORDER BY name";
$residences_result = $conn->query($residences_query);
$residences = [];
if ($residences_result && $residences_result->num_rows > 0) {
    while ($row = $residences_result->fetch_assoc()) {
        $residences[] = $row;
    }
}

// التحقق من وجود مدير في النظام
$check_admin = $conn->query("SELECT * FROM residence_managers WHERE role = 'manager' LIMIT 1");
$admin_exists = $check_admin->num_rows > 0;

// إذا تم إرسال النموذج
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // التحقق من رمز CSRF
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "خطأ في التحقق من الأمان. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.";
        $message_type = "danger";
    } else {
        $username = $_POST['username'];
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];
        $residence_name = $_POST['residence_name']; // إضافة حقل الإقامة الجامعية
        
        // التحقق من تطابق كلمات المرور
        if ($password !== $confirm_password) {
            $message = "كلمات المرور غير متطابقة";
            $message_type = "danger";
        } else {
            // تشفير كلمة المرور باستخدام password_hash
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // استخدام الاستعلامات المُجهزة لمنع هجمات SQL Injection
            $stmt = $conn->prepare("INSERT INTO residence_managers (username, password, role, residence_name) VALUES (?, ?, 'manager', ?)");
            $stmt->bind_param("sss", $username, $hashed_password, $residence_name);
            
            if ($stmt->execute()) {
                $message = "تم إنشاء حساب المدير بنجاح!";
                $message_type = "success";
                // إعادة توجيه إلى صفحة تسجيل الدخول بعد 3 ثوان
                header("refresh:3;url=admin_login.php");
            } else {
                $message = "خطأ: " . $stmt->error;
                $message_type = "danger";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب المدير</title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .main-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .admin-form-card {
            max-width: 550px;
            width: 100%;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .admin-form-header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .admin-form-body {
            padding: 30px;
            background-color: white;
        }
        .form-floating {
            margin-bottom: 20px;
        }
        .password-toggle {
            cursor: pointer;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
        .site-header {
            background-color: #343a40;
            color: white;
            padding: 15px 0;
            text-align: center;
        }
        .site-footer {
            background-color: #343a40;
            color: white;
            padding: 15px 0;
            text-align: center;
            margin-top: auto;
        }
    </style>
</head>
<body>
    <header class="site-header">
        <div class="container">
            <h1 class="h3 mb-0">منصة إدارة الإقامات الجامعية</h1>
        </div>
    </header>

    <div class="main-content">
        <div class="container">
            <div class="admin-form-card mx-auto">
                <div class="admin-form-header">
                    <i class="fas fa-user-shield fa-3x mb-3"></i>
                    <h2 class="h4 mb-0">إنشاء حساب المدير</h2>
                </div>
                
                <div class="admin-form-body">
                    <?php if (!empty($message)): ?>
                        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                            <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!$admin_exists || isset($_GET['force'])): ?>
                        <form method="POST" action="">
                            <!-- إضافة حقل مخفي لرمز CSRF -->
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                            
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                                <label for="username">اسم المستخدم</label>
                            </div>
                            
                            <div class="form-floating mb-3 position-relative">
                                <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                                <label for="password">كلمة المرور</label>
                                <span class="password-toggle" onclick="togglePasswordVisibility('password', 'togglePassword')">
                                    <i class="fas fa-eye" id="togglePassword"></i>
                                </span>
                            </div>
                            
                            <div class="form-floating mb-3 position-relative">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="تأكيد كلمة المرور" required>
                                <label for="confirm_password">تأكيد كلمة المرور</label>
                                <span class="password-toggle" onclick="togglePasswordVisibility('confirm_password', 'toggleConfirmPassword')">
                                    <i class="fas fa-eye" id="toggleConfirmPassword"></i>
                                </span>
                            </div>
                            
                            <div class="form-floating mb-4">
                                <select class="form-select" id="residence_name" name="residence_name" >
                                    <option value="">-- اختر الإقامة الجامعية --</option>
                                    <?php foreach ($residences as $residence): ?>
                                        <option value="<?php echo $residence['name']; ?>">
                                            <?php echo $residence['name'] . ' (' . $residence['gender'] . ')'; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label for="residence_name">الإقامة الجامعية</label>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>
                                    إنشاء الحساب
                                </button>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <p class="mb-2">يوجد بالفعل حساب مدير في النظام.</p>
                            <p class="mb-0">إذا كنت ترغب في إنشاء حساب مدير جديد، <a href="?force=1" class="alert-link">انقر هنا</a>.</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="admin_login.php" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                العودة إلى صفحة تسجيل الدخول
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <footer class="site-footer">
        <div class="container">
            <p class="mb-0">&copy; منصة التحويلات بين الإقامات الجامعية 2025</p>
        </div>
    </footer>

    <!-- إضافة سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- سكريبت لإظهار/إخفاء كلمة المرور -->
    <script>
        function togglePasswordVisibility(inputId, toggleId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(toggleId);
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>

<?php
$conn->close();
?>