-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.7.5
-- https://www.phpmyadmin.net/
--
-- Hôte : localhost
-- <PERSON><PERSON><PERSON><PERSON> le :  Dim 06 avr. 2025 à 06:36
-- Version du serveur :  5.6.34
-- Version de PHP :  7.1.11

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données :  `base_transfer`
--

-- --------------------------------------------------------

--
-- Structure de la table `requests`
--

CREATE TABLE `requests` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `residence_requested` varchar(100) NOT NULL,
  `status` enum('pending','accepted','rejected') DEFAULT 'pending',
  `release_status` enum('yes','no') DEFAULT 'no',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `requests`
--

INSERT INTO `requests` (`id`, `student_id`, `residence_requested`, `status`, `release_status`, `created_at`) VALUES
(7, 4, 'Ø°Ø¨ÙŠØ­ Ø¹Ø¨Ø¯ Ø§Ù„Ù‚Ø§Ø¯Ø±', 'accepted', 'yes', '2025-04-04 19:19:27'),
(8, 5, 'Ø­Ø³ÙˆÙ†ÙŠ Ø±Ù…Ø¶Ø§Ù†', 'accepted', 'yes', '2025-04-04 19:20:40'),
(9, 3, 'Ø¨Ù† Ø¨ÙˆÙ„Ø¹ÙŠØ¯ Ù…Ø­Ù…Ø¯', 'accepted', 'yes', '2025-04-04 22:00:25');

-- --------------------------------------------------------

--
-- Structure de la table `residences`
--

CREATE TABLE `residences` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `capacity` int(11) NOT NULL,
  `current_residents` int(11) NOT NULL DEFAULT '0',
  `gender` enum('ذكور','اناث') NOT NULL,
  `address` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `residences`
--

INSERT INTO `residences` (`id`, `name`, `capacity`, `current_residents`, `gender`, `address`) VALUES
(1, 'Ø­Ø³ÙˆÙ†ÙŠ Ø±Ù…Ø¶Ø§Ù†', 1000, 1500, '', 'Ø­ÙŠ 500 Ù…Ø³ÙƒÙ†'),
(2, 'Ù†ÙˆÙŠÙˆØ§Øª Ù…ÙˆØ³Ù‰ Ø§Ù„Ø£Ø­Ù…Ø¯ÙŠ', 1000, 2000, '', 'Ø­ÙŠ Ø¨Ù„Ù…Ø¬Ù†Ø­ Ø§Ù„Ù…Ø³ÙŠÙ„Ø©'),
(3, 'Ø°Ø¨ÙŠØ­ Ø¹Ø¨Ø¯ Ø§Ù„Ù‚Ø§Ø¯Ø±', 2000, 2000, '', 'Ø­ÙŠ 500 Ù…Ø³ÙƒÙ†'),
(4, 'Ø¨Ù† Ø¨ÙˆÙ„Ø¹ÙŠØ¯ Ù…Ø­Ù…Ø¯', 1998, 2000, '', 'Ø­ÙŠ 500 Ù…Ø³ÙƒÙ†'),
(5, '500 Ø³Ø±ÙŠØ± cfa', 500, 400, '', 'Ø­ÙŠ Ø³ÙˆÙ†ÙŠØªØ§ÙƒØ³'),
(6, '500 Ø³Ø±ÙŠØ± Ø¨ÙˆØ³Ø¹Ø§Ø¯Ø©', 500, 1000, '', 'Ø¨ÙˆØ³Ø¹Ø§Ø¯Ø©'),
(7, 'Ø§Ù„Ù…Ø¯ÙŠØ±ÙŠØ©', 1, 11, '', 'Ø¨Ø¨');

-- --------------------------------------------------------

--
-- Structure de la table `residence_managers`
--

CREATE TABLE `residence_managers` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('manager','residence_manager') NOT NULL,
  `residence_name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `residence_managers`
--

INSERT INTO `residence_managers` (`id`, `username`, `password`, `role`, `residence_name`) VALUES
(3, 'Ø¹Ø§ÙŠØ¨ Ø¹Ù…Ø±', 'kantous04021977', 'manager', 'Ø§Ù„Ù…Ø¯ÙŠØ±ÙŠØ©'),
(16, 'Ø¨Ø¯Ø±Ùˆ', '1234', 'residence_manager', 'Ù†ÙˆÙŠÙˆØ§Øª Ù…ÙˆØ³Ù‰ Ø§Ù„Ø£Ø­Ù…Ø¯ÙŠ'),
(17, 'Ø¹Ù„Ø§Ø¡', '1234', 'residence_manager', 'Ø¨Ù† Ø¨ÙˆÙ„Ø¹ÙŠØ¯ Ù…Ø­Ù…Ø¯');

-- --------------------------------------------------------

--
-- Structure de la table `students`
--

CREATE TABLE `students` (
  `id` int(11) NOT NULL,
  `baccalaureate_number` varchar(20) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `residence` varchar(100) NOT NULL,
  `birth_date` date NOT NULL,
  `birth_place` varchar(100) NOT NULL,
  `specialization` varchar(100) NOT NULL,
  `study_year` varchar(20) NOT NULL,
  `gender` varchar(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Déchargement des données de la table `students`
--

INSERT INTO `students` (`id`, `baccalaureate_number`, `last_name`, `first_name`, `residence`, `birth_date`, `birth_place`, `specialization`, `study_year`, `gender`) VALUES
(3, '12345678', 'Ø¹Ø§ÙŠØ¨', 'Ø¹Ù…Ø±', 'Ù†ÙˆÙŠÙˆØ§Øª Ù…ÙˆØ³Ù‰ Ø§Ù„Ø£Ø­Ù…Ø¯ÙŠ', '1977-02-04', 'Ø§Ù„Ù…Ø³ÙŠÙ„Ø©', 'Ø§Ø¹Ù„Ø§Ù…Ù… Ø§Ù„ÙŠ', '2', 'Ø°ÙƒØ±'),
(4, '32658978', 'Ù…ÙŠÙ„ÙŠ', 'Ù…Ø­Ù…Ø¯', 'Ù†ÙˆÙŠÙˆØ§Øª Ù…ÙˆØ³Ù‰ Ø§Ù„Ø£Ø­Ù…Ø¯ÙŠ', '1988-02-11', 'Ø§Ù„Ù…Ø³ÙŠÙ„Ø©', 'Ø§Ø¯Ø¨ Ø¹Ø±Ø¨ÙŠ', '3', 'Ø°ÙƒØ±'),
(5, '45698521', 'Ø¹Ù…Ø±ÙˆÙ†', 'Ø®Ø¯ÙŠØ¬Ø©', 'Ø¨Ù† Ø¨ÙˆÙ„Ø¹ÙŠØ¯ Ù…Ø­Ù…Ø¯', '1990-11-22', 'Ø§Ù„Ø¨Ø±Ø¬', 'Ø¹Ù„ÙˆÙ… Ø³ÙŠØ§Ø³ÙŠØ©', '1', 'Ø§Ù†Ø«Ù‰');

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `requests`
--
ALTER TABLE `requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `student_id` (`student_id`);

--
-- Index pour la table `residences`
--
ALTER TABLE `residences`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `residence_managers`
--
ALTER TABLE `residence_managers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Index pour la table `students`
--
ALTER TABLE `students`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `baccalaureate_number` (`baccalaureate_number`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `requests`
--
ALTER TABLE `requests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `residences`
--
ALTER TABLE `residences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT pour la table `residence_managers`
--
ALTER TABLE `residence_managers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT pour la table `students`
--
ALTER TABLE `students`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `requests`
--
ALTER TABLE `requests`
  ADD CONSTRAINT `requests_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
