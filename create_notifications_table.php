<?php
// This script creates the notifications table if it doesn't exist

// Start session for security
session_start();

// Security check - Allow only in development environment or for administrators
$allowed = false;

// Check if user is admin
if (isset($_SESSION['admin']) && $_SESSION['admin']['role'] == 'manager') {
    $allowed = true;
}

// Check if it's a localhost request (development environment)
if (in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1'])) {
    $allowed = true;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء جدول الإشعارات</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <div class="container py-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">إنشاء جدول الإشعارات</h3>
            </div>
            <div class="card-body">
            <?php
            if ($allowed) {
                // Include database connection
                require_once 'db_connect.php';

                echo '<h4>جاري إنشاء جدول الإشعارات...</h4>';
                
                // SQL query to create the notifications table
                $sql = "CREATE TABLE IF NOT EXISTS notifications (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    user_type ENUM('admin', 'student') NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    message TEXT NOT NULL,
                    type ENUM('info', 'success', 'warning', 'danger') DEFAULT 'info',
                    link VARCHAR(255) DEFAULT NULL,
                    is_read BOOLEAN DEFAULT 0,
                    read_at DATETIME DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8";
                
                // Execute the query
                if ($conn->query($sql) === TRUE) {
                    echo '<div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم إنشاء جدول الإشعارات بنجاح!
                    </div>';
                    echo '<p>يمكنك الآن <a href="dashboard.php" class="btn btn-primary btn-sm">الذهاب إلى لوحة التحكم</a> بدون مشاكل.</p>';
                } else {
                    echo '<div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في إنشاء جدول الإشعارات: ' . $conn->error . '
                    </div>';
                }
                
                // Close the connection
                $conn->close();
            } else {
                echo '<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    غير مسموح بالوصول. يجب أن تكون مسؤولاً للقيام بهذه العملية.
                </div>';
            }
            ?>
            </div>
            <div class="card-footer">
                <a href="index.html" class="btn btn-secondary">العودة إلى الصفحة الرئيسية</a>
            </div>
        </div>
    </div>
</body>
</html> 