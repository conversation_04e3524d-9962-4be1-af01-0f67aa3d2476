<?php
// التحقق من تسجيل الدخول
session_start();
require_once 'config.php'; // إضافة ملف الإعدادات المركزي

// التحقق من تسجيل دخول المسؤول
if (!isset($_SESSION['admin']) || $_SESSION['admin']['role'] != 'manager') {
    header("Location: admin_login.php");
    exit();
}

include 'db_connect.php';
include 'db_helper.php'; // إضافة ملف المساعدة لقاعدة البيانات

// الحصول على معلومات المسؤول
$admin_username = $_SESSION['admin']['username'];
$residence_name = $_SESSION['admin']['residence_name'];


// استعلامات الإحصائيات
// 1. إجمالي عدد الطلبات
$total_requests_query = "SELECT COUNT(*) as total FROM requests";
$total_requests_result = $conn->query($total_requests_query);
$total_requests = $total_requests_result->fetch_assoc()['total'];

// 2. عدد الطلبات حسب الحالة
$status_query = "SELECT status, COUNT(*) as count FROM requests GROUP BY status";
$status_result = $conn->query($status_query);
$status_stats = [];
while ($row = $status_result->fetch_assoc()) {
    $status_stats[$row['status']] = $row['count'];
}

// 3. عدد الطلبات حسب حالة التبرئة
$release_query = "SELECT release_status, COUNT(*) as count FROM requests GROUP BY release_status";
$release_result = $conn->query($release_query);
$release_stats = [];
while ($row = $release_result->fetch_assoc()) {
    $release_stats[$row['release_status']] = $row['count'];
}

// 4. عدد الطلبات حسب الإقامة المطلوبة
$residence_query = "SELECT residence_requested, COUNT(*) as count FROM requests GROUP BY residence_requested ORDER BY count DESC LIMIT 5";
$residence_result = $conn->query($residence_query);

// 5. عدد الطلبات حسب التخصص
$specialization_query = "SELECT s.specialization, COUNT(*) as count 
                        FROM requests r 
                        JOIN students s ON r.student_id = s.id 
                        GROUP BY s.specialization 
                        ORDER BY count DESC 
                        LIMIT 5";
$specialization_result = $conn->query($specialization_query);

// 6. عدد الطلبات حسب السنة الدراسية
$year_query = "SELECT s.study_year, COUNT(*) as count 
              FROM requests r 
              JOIN students s ON r.student_id = s.id 
              GROUP BY s.study_year 
              ORDER BY count DESC";
$year_result = $conn->query($year_query);

// 7. آخر 5 طلبات
$recent_query = "SELECT r.id, s.last_name, s.first_name, r.residence_requested, r.status, r.created_at 
                FROM requests r 
                JOIN students s ON r.student_id = s.id 
                ORDER BY r.created_at DESC 
                LIMIT 5";
$recent_result = $conn->query($recent_query);

// دالة لترجمة حالة الطلب
function translateStatus($status) {
    switch($status) {
        case 'pending':
            return 'قيد الدراسة';
        case 'accepted':
            return 'مقبول';
        case 'rejected':
            return 'مرفوض';
        default:
            return $status;
    }
}

// دالة لترجمة حالة التبرئة
function translateReleaseStatus($status) {
    return ($status == 'yes') ? 'تمت التبرئة' : 'لم تتم التبرئة';
}

// عدد الطلبة
$students_count = db_count("students");

// عدد الطلبات حسب الحالة
$pending_requests = db_count("requests", "status = 'pending'");
$accepted_requests = db_count("requests", "status = 'accepted'");
$rejected_requests = db_count("requests", "status = 'rejected'");
$total_requests = $pending_requests + $accepted_requests + $rejected_requests;

// عدد الطلبات في الأسبوع الحالي
$weekly_requests = db_count("requests", "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");

// عدد الإقامات والسعة
$residences_data = db_fetch_all("SELECT name, capacity, current_residents, gender FROM residences ORDER BY name");
$total_capacity = 0;
$total_residents = 0;
$male_residences = 0;
$female_residences = 0;

foreach ($residences_data as $residence) {
    $total_capacity += $residence['capacity'];
    $total_residents += $residence['current_residents'];
    if ($residence['gender'] == 'ذكور') {
        $male_residences++;
    } else {
        $female_residences++;
    }
}

// حساب نسبة الإشغال
$occupancy_rate = ($total_capacity > 0) ? round(($total_residents / $total_capacity) * 100, 2) : 0;

// الطلبات الأخيرة
$recent_requests = db_fetch_all(
    "SELECT r.id, r.status, r.created_at, s.first_name, s.last_name, s.residence, r.residence_requested 
     FROM requests r 
     JOIN students s ON r.student_id = s.id 
     ORDER BY r.created_at DESC 
     LIMIT 10"
);

// آخر المستخدمين النشطين
$active_admins = db_fetch_all(
    "SELECT username, role, residence_name, last_login 
     FROM residence_managers 
     WHERE last_login IS NOT NULL 
     ORDER BY last_login DESC 
     LIMIT 5"
);

// احصائيات النظام
$system_info = get_system_info();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم | <?php echo SITE_NAME; ?></title>
    <!-- إضافة Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- إضافة Chart.js للرسوم البيانية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- أنماط CSS الأساسية -->
    <link rel="stylesheet" href="styles.css">
    <!-- أنماط الوضع المظلم -->
    <link rel="stylesheet" href="styles_dark.css">
    <style>
        .stats-card {
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
            height: 100%;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .chart-container {
            position: relative;
            margin: auto;
            height: 250px;
        }
        .sidebar {
            background-color: #343a40;
            min-height: 100vh;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            margin: 0.2rem 0;
            border-radius: 5px;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .sidebar .nav-link.active {
            background-color: var(--bs-primary);
            color: white;
        }
        .sidebar-heading {
            font-size: 0.85rem;
            text-transform: uppercase;
            padding: 1rem;
            opacity: 0.6;
        }
        .main-content {
            padding: 20px;
        }
        .user-welcome {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .recent-activity {
            max-height: 400px;
            overflow-y: auto;
        }
        .quick-actions a {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 15px;
            text-decoration: none;
            color: var(--bs-dark);
            transition: all 0.3s;
        }
        .quick-actions a:hover {
            background-color: var(--bs-primary);
            color: white;
        }
        .quick-actions i {
            font-size: 2rem;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- المسار الجانبي -->
            <div class="col-md-3 col-lg-2 p-0">
                <div class="sidebar h-100 d-flex flex-column">
                    <div class="p-3 border-bottom">
                        <h5 class="m-0 text-white"><?php echo SITE_NAME; ?></h5>
                    </div>
                    <div class="p-3 border-bottom">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-user-circle fa-2x text-white"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="text-white"><?php echo $admin_username; ?></div>
                                <small class="text-muted"><?php echo $residence_name; ?></small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Notifications button -->
                    <?php 
                    include_once 'notifications.php';
                    $unread_notifications = get_unread_notifications_count($_SESSION['admin']['id'], 'admin');
                    ?>
                    <div class="p-2 border-bottom">
                        <a href="view_notifications.php" class="btn btn-outline-light w-100 d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-bell me-2"></i> الإشعارات</span>
                            <?php if ($unread_notifications > 0): ?>
                                <span class="badge bg-danger"><?php echo $unread_notifications; ?></span>
                            <?php endif; ?>
                        </a>
                    </div>
                    
                    <ul class="nav flex-column p-2">
                    <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                            <a class="nav-link" href="manage_requests.php">
                            <i class="fas fa-exchange-alt me-2"></i>
                            إدارة الطلبات
                        </a>
                    </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_students.php">
                                <i class="fas fa-user-graduate me-2"></i>
                                إدارة الطلبة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_residences.php">
                                <i class="fas fa-building me-2"></i>
                                إدارة الإقامات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="manage_accounts.php">
                                <i class="fas fa-users-cog me-2"></i>
                                إدارة الحسابات
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-3">
                    
                    <div class="sidebar-heading">
                        أدوات
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="import_students.php">
                                <i class="fas fa-file-import me-2"></i>
                                استيراد بيانات
                            </a>
                        </li>
                        <?php if (FEATURE_EXPORT_DATA): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="export_data.php">
                                <i class="fas fa-file-export me-2"></i>
                                تصدير بيانات
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (FEATURE_AUTOMATIC_BACKUP): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="backup.php">
                                <i class="fas fa-database me-2"></i>
                                النسخ الاحتياطي
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                    
                    <hr class="my-3">
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                </ul>
                </div>
            </div>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="user-welcome d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h3 class="mb-1">مرحباً، <?php echo $_SESSION['admin']['username']; ?></h3>
                        <p class="text-muted mb-0">آخر تسجيل دخول: <?php echo date('Y-m-d H:i'); ?></p>
                    </div>
                    <div>
                        <span class="badge bg-primary p-2">
                            <?php echo $_SESSION['admin']['role'] == 'manager' ? 'مدير النظام' : 'مدير إقامة'; ?>
                        </span>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stats-card p-3 bg-primary text-white">
                            <div class="d-flex justify-content-between">
                                    <div>
                                    <h6 class="mb-0">إجمالي الطلبة</h6>
                                    <div class="stats-number"><?php echo $students_count; ?></div>
                                    </div>
                                <div class="stats-icon">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card p-3 bg-success text-white">
                            <div class="d-flex justify-content-between">
                                    <div>
                                    <h6 class="mb-0">الطلبات المقبولة</h6>
                                    <div class="stats-number"><?php echo $accepted_requests; ?></div>
                                    </div>
                                <div class="stats-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card p-3 bg-warning text-dark">
                            <div class="d-flex justify-content-between">
                                    <div>
                                    <h6 class="mb-0">طلبات قيد الانتظار</h6>
                                    <div class="stats-number"><?php echo $pending_requests; ?></div>
                                    </div>
                                <div class="stats-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card p-3 bg-danger text-white">
                            <div class="d-flex justify-content-between">
                                    <div>
                                    <h6 class="mb-0">الطلبات المرفوضة</h6>
                                    <div class="stats-number"><?php echo $rejected_requests; ?></div>
                                    </div>
                                <div class="stats-icon">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <!-- إجمالي معلومات الإقامات -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-building me-2 text-primary"></i>
                                    إحصائيات الإقامات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>نسبة الإشغال</span>
                                        <span><strong><?php echo $occupancy_rate; ?>%</strong></span>
                                    </div>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar <?php echo $occupancy_rate > 90 ? 'bg-danger' : ($occupancy_rate > 75 ? 'bg-warning' : 'bg-success'); ?>" role="progressbar" style="width: <?php echo $occupancy_rate; ?>%" aria-valuenow="<?php echo $occupancy_rate; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h3><?php echo count($residences_data); ?></h3>
                                        <p class="text-muted mb-0">إجمالي الإقامات</p>
                                    </div>
                                    <div class="col-6">
                                        <h3><?php echo $total_capacity; ?></h3>
                                        <p class="text-muted mb-0">السعة الإجمالية</p>
                        </div>
                    </div>
                                <div class="chart-container mt-4">
                                    <canvas id="occupancyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- الطلبات حسب الحالة -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-pie me-2 text-primary"></i>
                                    إحصائيات الطلبات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="requestsChart"></canvas>
                                </div>
                                <div class="row text-center mt-4">
                                    <div class="col-4">
                                        <h4><?php echo $total_requests; ?></h4>
                                        <p class="text-muted">إجمالي الطلبات</p>
                                    </div>
                                    <div class="col-4">
                                        <h4><?php echo $weekly_requests; ?></h4>
                                        <p class="text-muted">طلبات الأسبوع</p>
                            </div>
                                    <div class="col-4">
                                        <h4><?php echo round(($accepted_requests / ($total_requests ?: 1)) * 100); ?>%</h4>
                                        <p class="text-muted">نسبة القبول</p>
                        </div>
                    </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                <!-- آخر الطلبات -->
                    <div class="col-md-8 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-list-alt me-2 text-primary"></i>
                                    آخر الطلبات
                                </h5>
                                <a href="manage_requests.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th scope="col">#</th>
                                                <th scope="col">الطالب</th>
                                                <th scope="col">من إقامة</th>
                                                <th scope="col">إلى إقامة</th>
                                                <th scope="col">التاريخ</th>
                                                <th scope="col">الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($recent_requests)): ?>
                                                <tr>
                                                    <td colspan="6" class="text-center py-3">لا توجد طلبات حتى الآن</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($recent_requests as $request): ?>
                                                    <tr>
                                                        <td><?php echo $request['id']; ?></td>
                                                        <td><?php echo $request['first_name'] . ' ' . $request['last_name']; ?></td>
                                                        <td><?php echo $request['residence']; ?></td>
                                                        <td><?php echo $request['residence_requested']; ?></td>
                                                        <td><?php echo date('Y-m-d', strtotime($request['created_at'])); ?></td>
                                                        <td>
                                                            <?php if ($request['status'] == 'pending'): ?>
                                                                <span class="badge bg-warning text-dark">قيد الانتظار</span>
                                                            <?php elseif ($request['status'] == 'accepted'): ?>
                                                                <span class="badge bg-success">مقبول</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">مرفوض</span>
                                                            <?php endif; ?>
                                                </td>
                                            </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإجراءات السريعة -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bolt me-2 text-primary"></i>
                                    إجراءات سريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="quick-actions row">
                                    <div class="col-6 mb-3">
                                        <a href="manage_requests.php?status=pending">
                                            <i class="fas fa-tasks text-warning"></i>
                                            <span>طلبات جديدة</span>
                                        </a>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <a href="create_account.php">
                                            <i class="fas fa-user-plus text-success"></i>
                                            <span>إضافة مستخدم</span>
                                        </a>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <a href="import_students.php">
                                            <i class="fas fa-file-import text-primary"></i>
                                            <span>استيراد طلبة</span>
                                        </a>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <a href="manage_residences.php">
                                            <i class="fas fa-building text-info"></i>
                                            <span>الإقامات</span>
                                        </a>
                                    </div>
                                </div>
                                
                                <!-- آخر المستخدمين النشطين -->
                                <h6 class="mt-4 mb-3">آخر المستخدمين النشطين</h6>
                                <div class="list-group">
                                    <?php if (empty($active_admins)): ?>
                                        <div class="text-center text-muted py-3">لا توجد بيانات متاحة</div>
                                    <?php else: ?>
                                        <?php foreach ($active_admins as $admin): ?>
                                            <div class="list-group-item list-group-item-action py-3">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo $admin['username']; ?></h6>
                                                    <small><?php echo date('H:i', strtotime($admin['last_login'])); ?></small>
                                                </div>
                                                <p class="mb-1 small">
                                                    <?php echo $admin['role'] == 'manager' ? 'مدير النظام' : 'مدير إقامة: ' . $admin['residence_name']; ?>
                                                </p>
                                                <small><?php echo date('Y-m-d', strtotime($admin['last_login'])); ?></small>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- ملف JavaScript الرئيسي للتطبيق -->
    <script src="app.js"></script>
    
    <script>
        // رسم الرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني دائري لحالة الطلبات
            const statusChartCtx = document.getElementById('statusChart').getContext('2d');
            const statusChart = new Chart(statusChartCtx, {
                type: 'doughnut',
                data: {
                    labels: ['قيد الدراسة', 'مقبول', 'مرفوض'],
                    datasets: [{
                        data: [<?php echo $pending_requests; ?>, <?php echo $accepted_requests; ?>, <?php echo $rejected_requests; ?>],
                        backgroundColor: ['#ffc107', '#198754', '#dc3545'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Cairo, sans-serif'
                                }
                            }
                        }
                    }
                }
            });
            
            // تحديث الرسومات البيانية عند تغيير الوضع المظلم
            document.addEventListener('darkModeToggle', function() {
                statusChart.update();
            });
        });
    </script>
</body>
</html>

<?php
$conn->close();
?>